<template>
  <el-dialog :title="title" :visible.sync="visible" width="500px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="分组名称" prop="groupName">
        <el-input v-model="form.groupName" placeholder="请输入分组名称" />
      </el-form-item>
      <el-form-item label="分组描述" prop="groupDescription">
        <el-input v-model="form.groupDescription" type="textarea" placeholder="请输入分组描述 (可选)" />
      </el-form-item>
      <el-form-item label="分组颜色" prop="groupColor">
         <el-radio-group v-model="form.groupColor">
            <el-radio-button v-for="color in colors" :key="color" :label="color">
              <span class="color-dot" :style="{ backgroundColor: color }"></span>
            </el-radio-button>
          </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addGroup, updateGroup } from "@/api/yqkeysignal/group";

export default {
  name: "GroupForm",
  data() {
    return {
      visible: false,
      title: '',
      form: {},
      rules: {
        groupName: [
          { required: true, message: "分组名称不能为空", trigger: "blur" }
        ],
        groupColor: [
          { required: true, message: "请选择一个分组颜色", trigger: "change" }
        ]
      },
      // 预设颜色
      colors: ['#F56C6C', '#67C23A', '#409EFF', '#E6A23C', '#909399', '#B87FEA', '#5AC8FA', '#FF5B5A']
    };
  },
  methods: {
    open(mode = 'add', data = null) {
      this.reset();
      if (mode === 'add') {
        this.title = "新建分组";
        this.form.groupColor = this.colors[0]; // 默认选第一个颜色
      } else {
        this.title = "编辑分组";
        this.form = { ...data, id: data.groupId };
      }
      this.visible = true;
    },
    reset() {
      this.form = {
        id: undefined,
        groupName: undefined,
        groupDescription: undefined,
        groupColor: this.colors[0]
      };
      this.resetForm("form");
    },
    cancel() {
      this.visible = false;
      this.reset();
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id) {
            updateGroup(this.form.id, this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.visible = false;
              this.$emit("submit-success");
            });
          } else {
            addGroup(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.visible = false;
              this.$emit("submit-success");
            });
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.color-dot {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  vertical-align: middle;
}
.el-radio-button__inner {
  padding: 8px;
}
</style>