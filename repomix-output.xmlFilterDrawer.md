# Directory Structure
```
src/views/yqmonitor/components/MainContent/FilterDrawer.vue
src/views/yqmonitor/components/MainContent/index.vue
```

# Files

## File: src/views/yqmonitor/components/MainContent/FilterDrawer.vue
```vue
<template>
  <el-drawer :visible.sync="localVisible" title="筛选" size="800px" @close="handleClose">
    <el-form :model="form" label-width="120px">
      <el-form-item label="时间范围">
        <el-button-group>
          <el-button :type="timeRangeType === 'all' ? 'primary' : 'default'"
            @click="selectTimeRange('all')">全部</el-button>
          <el-button :type="timeRangeType === 'work' ? 'primary' : 'default'"
            @click="selectTimeRange('work')">工作时间</el-button>
          <el-button :type="timeRangeType === '24h' ? 'primary' : 'default'"
            @click="selectTimeRange('24h')">近24小时</el-button>
          <el-button :type="timeRangeType === '7d' ? 'primary' : 'default'"
            @click="selectTimeRange('7d')">近7天</el-button>
          <el-button :type="timeRangeType === 'today' ? 'primary' : 'default'"
            @click="selectTimeRange('today')">当天</el-button>
          <el-button :type="timeRangeType === 'custom' ? 'primary' : 'default'"
            @click="selectTimeRange('custom')">自定义时间</el-button>
        </el-button-group>
        <el-date-picker v-if="timeRangeType === 'custom'" v-model="dateRange" type="datetimerange" range-separator="—"
          start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss" style="margin-top: 10px" />
      </el-form-item>
      <el-form-item label="倾向性">
        <el-checkbox-group v-model="sensitivitysUI" @change="handleSensitivitysUIChange">
          <el-checkbox-button v-for="item in dict.type.filter_sentiment" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="AI舆情类型">
        <el-checkbox-group v-model="aiSentimentTypesUI" @change="handleAiSentimentTypesUIChange">
          <el-checkbox-button v-for="item in dict.type.ai_public_opinion_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="作品发布类型">
        <el-checkbox-group v-model="publishTypesUI" @change="handlePublishTypesUIChange">
          <el-checkbox-button v-for="item in dict.type.content_publish_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="账号认证类型">
        <el-checkbox-group v-model="accountAuthTypesUI" @change="handleAccountAuthTypesUIChange">
          <el-checkbox-button v-for="item in dict.type.account_auth_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="信源平台">
        <el-checkbox-group v-model="platformsUI" @change="handlePlatformsUIChange">
          <el-checkbox-button v-for="item in dict.type.content_platform" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="舆情特征">
        <el-checkbox-group v-model="negativeSentimentTagsUI" @change="handleNegativeSentimentTagsUIChange">
          <el-checkbox-button v-for="item in dict.type.negative_public_opinion_tag" :key="item.value"
            :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="信源等级">
        <el-checkbox-group v-model="sourceLevelsUI" @change="handleSourceLevelsUIChange">
          <el-checkbox-button v-for="item in dict.type.source_level" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="噪音">
        <el-radio-group v-model="noiseUI">
          <el-radio-button v-for="item in dict.type.is_noise" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="机构词过滤">
        <div class="filter-group">
          <el-radio-group v-model="institutionOpenFlagUI">
            <el-radio-button v-for="item in dict.type.institution_word_filter" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </el-form-item>
      <el-form-item label="相似信息">
        <el-radio-group v-model="similarUI">
          <el-radio-button v-for="item in dict.type.is_similar" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="阅读状态">
        <el-radio-group v-model="readStatusUI">
          <el-radio-button v-for="item in readStatusDict" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="粉丝数量">
        <el-button-group>
          <el-button v-for="item in fansCountOptions" :key="item.value" :type="fansCountSelected === item.value && !fansCountCustom
            ? 'primary'
            : 'default'
            " style="margin-bottom: 10px; margin-right: 10px" @click="handleFansCountSelect(item.value)">
            {{ item.label }}
          </el-button>
          <el-button :type="fansCountCustom ? 'primary' : 'default'" @click="handleFansCountCustom">自定义</el-button>
        </el-button-group>
        <div v-if="fansCountCustom" style="margin-top: 10px">
          <el-input-number v-model="form.minFansCount" :min="0" placeholder="最小" style="width: 100px" :controls="false"
            @change="(val) => handleFansCountInput('minFansCount', val)" />
          <span style="margin: 0 8px">~</span>
          <el-input-number v-model="form.maxFansCount" :min="0" placeholder="最大" style="width: 100px" :controls="false"
            @change="(val) => handleFansCountInput('maxFansCount', val)" />
        </div>
      </el-form-item>
      <el-form-item label="必含词">
        <div v-for="(group, index) in form.mustContainGroups" :key="group.id" class="keyword-group">
          <el-input v-model="group.value" placeholder="词组内多个词请用空格分隔" style="width: 300px" />
          <el-button v-if="form.mustContainGroups.length > 1" type="danger" icon="el-icon-minus" circle size="mini"
            class="keyword-group-btn" @click="removeMustContainGroup(group.id)" />
          <el-button v-if="index === form.mustContainGroups.length - 1" type="primary" icon="el-icon-plus" circle
            size="mini" class="keyword-group-btn" @click="addMustContainGroup" />
        </div>
      </el-form-item>
      <!-- <el-form-item label="任意词">
        <el-input
          v-model="form.shouldContain"
          placeholder="多个词请用空格分隔"
          style="width: 300px"
        />
      </el-form-item> -->
      <el-form-item label="排除词">
        <div v-for="(group, index) in form.mustNotContainGroups" :key="group.id" class="keyword-group">
          <el-input v-model="group.value" placeholder="词组内多个词请用空格分隔" style="width: 300px" />
          <el-button v-if="form.mustNotContainGroups.length > 1" type="danger" icon="el-icon-minus" circle size="mini"
            class="keyword-group-btn" @click="removeMustNotContainGroup(group.id)" />
          <el-button v-if="index === form.mustNotContainGroups.length - 1" type="primary" icon="el-icon-plus" circle
            size="mini" class="keyword-group-btn" @click="addMustNotContainGroup" />
        </div>
      </el-form-item>
      <el-form-item label="标题排除词">
        <el-input v-model="form.titleMustNotContain" placeholder="多个词请用空格分隔" style="width: 300px" />
      </el-form-item>
    </el-form>
    <div class="filter-drawer-footer">
      <el-button class="btn-reset" @click="resetForm">重置</el-button>
      <el-button type="primary" @click="handleSubmit">应用</el-button>
      <el-button class="btn-save" type="warning" @click="handleSubmitAndSave">应用并保存</el-button>
    </div>
  </el-drawer>
</template>
<script>
import { getDateRangeByType } from '@/utils/yqmonitorTool'
export default {
  name: 'FilterDrawer',
  dicts: [
    'filter_sentiment',
    'ai_public_opinion_type',
    'content_publish_type',
    'account_auth_type',
    'source_level',
    'is_noise',
    'is_similar',
    'read_status',
    'follow_read_status', // 新增字典
    'target_detect_read_status', // 新增字典
    'content_platform',
    'negative_public_opinion_tag',
    'institution_word_filter'
  ],
  props: {
    visible: Boolean,
    editingTagId: {
      type: String,
      default: ''
    },
    moduleType: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      form: {
        startTime: '',
        endTime: '',
        sensitivitys: [],
        aiSentimentTypes: [],
        publishTypes: [],
        accountAuthTypes: [],
        platforms: [],
        negativeSentimentTags: [],
        sourceLevels: [],
        noiseTypes: [],
        enableSimilarityDedup: 'fold',
        // readStatus, followReadStatus, targetDetectReadStatus 会被动态添加
        minFansCount: 0,
        maxFansCount: 0,
        mustContainGroups: [],
        mustNotContainGroups: [],
        shouldContain: '',
        titleMustNotContain: '',
        institutionOpenFlags: []
      },
      dateRange: [],
      sensitivitysUI: ['all'],
      sourceLevelsUI: ['all'],
      aiSentimentTypesUI: ['all'],
      publishTypesUI: ['all'],
      accountAuthTypesUI: ['all'],
      platformsUI: ['all'],
      negativeSentimentTagsUI: ['all'],
      readStatusUI: 'all', // 这个值会动态绑定到不同的状态
      noiseUI: 'all',
      institutionOpenFlagUI: 'all',
      similarUI: 'fold',
      localVisible: this.visible,
      fansCountOptions: [
        { label: '全部', value: 'all', min: 0, max: 0 },
        { label: '0 ~ 1000', value: '0-1000', min: 0, max: 1000 },
        { label: '1000 ~ 5000', value: '1000-5000', min: 1000, max: 5000 },
        { label: '5000 ~ 1w', value: '5000-10000', min: 5000, max: 10000 },
        { label: '1w ~ 10w', value: '10000-100000', min: 10000, max: 100000 },
        {
          label: '10w ~ 100w',
          value: '100000-1000000',
          min: 100000,
          max: 1000000
        },
        { label: '100w以上', value: '1000000-', min: 1000000, max: '' }
      ],
      fansCountSelected: '',
      fansCountCustom: false,
      timeRangeType: 'all'
    }
  },
  computed: {
    cachedTimeRangeType() {
      return this.$store.getters['yqmonitorMenu/cachedTimeRangeType']
    },
    // 新增计算属性
    readStatusKey() {
      const keyMap = {
        yqmonitor: 'readStatus',
        followedEvents: 'followReadStatus',
        targetMonitor: 'targetDetectReadStatus'
      };
      return keyMap[this.moduleType] || 'readStatus';
    },
    // 新增计算属性
    readStatusDict() {
      const dictMap = {
        yqmonitor: this.dict.type.read_status,
        followedEvents: this.dict.type.follow_read_status,
        targetMonitor: this.dict.type.target_detect_read_status
      };
      return dictMap[this.moduleType] || this.dict.type.read_status || [];
    }
  },
  watch: {
    visible(val) {
      this.localVisible = val
    },
    dateRange(val) {
      if (Array.isArray(val) && val.length === 2) {
        this.form.startTime = val[0]
        this.form.endTime = val[1]
      } else {
        this.form.startTime = ''
        this.form.endTime = ''
      }
    },
    sensitivitysUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.sensitivitys = []
      } else {
        this.form.sensitivitys = val.filter((item) => item !== 'all')
      }
    },
    aiSentimentTypesUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.aiSentimentTypes = []
      } else {
        this.form.aiSentimentTypes = val.filter((item) => item !== 'all')
      }
    },
    publishTypesUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.publishTypes = []
      } else {
        this.form.publishTypes = val.filter((item) => item !== 'all')
      }
    },
    accountAuthTypesUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.accountAuthTypes = []
      } else {
        this.form.accountAuthTypes = val.filter((item) => item !== 'all')
      }
    },
    platformsUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.platforms = []
      } else {
        this.form.platforms = val.filter((item) => item !== 'all')
      }
    },
    negativeSentimentTagsUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.negativeSentimentTags = []
      } else {
        this.form.negativeSentimentTags = val.filter((item) => item !== 'all')
      }
    },
    sourceLevelsUI(val) {
      if (val.includes('all') && val.length === 1) {
        this.form.sourceLevels = []
      } else {
        this.form.sourceLevels = val.filter((item) => item !== 'all')
      }
    },
    noiseUI(val) {
      this.form.noiseTypes = val === 'all' ? [] : [val]
    },
    institutionOpenFlagUI(val) {
      this.form.institutionOpenFlags = val === 'all' ? [] : [val]
    },
    readStatusUI(val) {
      // 移除旧的key
      delete this.form.readStatus;
      delete this.form.followReadStatus;
      delete this.form.targetDetectReadStatus;
      // 设置新的key
      if (val !== 'all') {
        this.form[this.readStatusKey] = [val];
      }
    },
    similarUI(val) {
      this.form.enableSimilarityDedup = val
    },
    localVisible(val) {
      console.log('localVisible', val)
      if (val && !this.$store.state.yqmonitorMenu.isOpeningFromTag) {
        this.initForm()
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    resetForm() {
      this.sensitivitysUI = ['all']
      this.aiSentimentTypesUI = ['all']
      this.publishTypesUI = ['all']
      this.accountAuthTypesUI = ['all']
      this.platformsUI = ['all']
      this.negativeSentimentTagsUI = ['all']
      this.sourceLevelsUI = ['all']
      this.readStatusUI = 'all'
      // 移除所有可能的已读状态key
      delete this.form.readStatus;
      delete this.form.followReadStatus;
      delete this.form.targetDetectReadStatus;
      this.noiseUI = 'all'
      this.institutionOpenFlagUI = 'all'
      this.similarUI = 'fold'
      this.form.noiseTypes = []
      this.form.readStatus = []
      this.form.institutionOpenFlags = []
      this.form.enableSimilarityDedup = 'fold'
      this.timeRangeType = 'all'
      this.dateRange = []
      this.fansCountSelected = 'all'
      this.fansCountCustom = false
      this.form.minFansCount = 0
      this.form.maxFansCount = 0
      this.form.mustContainGroups = [{ id: Date.now(), value: '' }]
      this.form.mustNotContainGroups = [{ id: Date.now() + 1, value: '' }]
      this.form.shouldContain = ''
      this.form.titleMustNotContain = ''
    },
    async handleSubmit() {
      try {
        await this.$confirm('确定要应用当前筛选条件吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        if (this.fansCountCustom) {
          if (Number(this.form.minFansCount) > Number(this.form.maxFansCount)) {
            this.$message.error('最小粉丝数不能大于最大粉丝数')
            return
          }
        }
        const formCopy = { ...this.form }
        // 清理所有可能的已读状态字段，以防万一
        delete formCopy.readStatus;
        delete formCopy.followReadStatus;
        delete formCopy.targetDetectReadStatus;
        // 根据当前的UI选项设置正确的字段
        if (this.readStatusUI !== 'all') {
          formCopy[this.readStatusKey] = [this.readStatusUI];
        } else {
          formCopy[this.readStatusKey] = [];
        }

        formCopy.mustContainGroups = this.form.mustContainGroups
          .map((g) => g.value.trim())
          .filter(Boolean)
        formCopy.mustNotContainGroups = this.form.mustNotContainGroups
          .map((g) => g.value.trim())
          .filter(Boolean)

        this.$emit('submit', formCopy)
        this.handleClose()
      } catch (e) {
        console.log(e)
      }
    },
    async handleSubmitAndSave() {
      try {
        await this.$confirm('确定要应用并保存当前筛选条件吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        if (this.fansCountCustom) {
          if (Number(this.form.minFansCount) > Number(this.form.maxFansCount)) {
            this.$message.error('最小粉丝数不能大于最大粉丝数')
            return
          }
        }
        const formCopy = { ...this.form }
        if (this.editingTagId) {
          this.$emit('updateFilterWord', {
            id: this.editingTagId,
            filterWord: formCopy
          })
          this.handleClose()
          return
        }
        this.$emit('submitAndSave', formCopy)
        this.handleClose()
      } catch (e) {
        console.log(e)
      }
    },
    handleFansCountSelect(value) {
      this.fansCountCustom = false
      this.fansCountSelected = value
      const option = this.fansCountOptions.find((opt) => opt.value === value)
      if (option) {
        this.form.minFansCount = option.min
        this.form.maxFansCount = option.max
      }
    },
    handleFansCountCustom() {
      this.fansCountCustom = true
      this.fansCountSelected = ''
      this.form.minFansCount = 0
      this.form.maxFansCount = 0
    },
    handleFansCountInput(key, val) {
      if (val === undefined || val === null || val === '') {
        this.form[key] = 0
      }
    },
    handleSensitivitysUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.sensitivitysUI = ['all']
        } else {
          this.sensitivitysUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.sensitivitysUI = ['all']
      }
    },
    handleAiSentimentTypesUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.aiSentimentTypesUI = ['all']
        } else {
          this.aiSentimentTypesUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.aiSentimentTypesUI = ['all']
      }
    },
    handlePublishTypesUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.publishTypesUI = ['all']
        } else {
          this.publishTypesUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.publishTypesUI = ['all']
      }
    },
    handleAccountAuthTypesUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.accountAuthTypesUI = ['all']
        } else {
          this.accountAuthTypesUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.accountAuthTypesUI = ['all']
      }
    },
    handlePlatformsUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.platformsUI = ['all']
        } else {
          this.platformsUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.platformsUI = ['all']
      }
    },
    handleNegativeSentimentTagsUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.negativeSentimentTagsUI = ['all']
        } else {
          this.negativeSentimentTagsUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.negativeSentimentTagsUI = ['all']
      }
    },
    handleSourceLevelsUIChange(val) {
      if (val.includes('all') && val.length > 1) {
        if (val[val.length - 1] === 'all') {
          this.sourceLevelsUI = ['all']
        } else {
          this.sourceLevelsUI = val.filter((item) => item !== 'all')
        }
      } else if (!val.length) {
        this.sourceLevelsUI = ['all']
      }
    },
    selectTimeRange(type) {
      this.timeRangeType = type
      this.dateRange = getDateRangeByType(type)
      if (type !== 'all' && type !== 'custom') {
        this.$store.commit('yqmonitorMenu/SET_CACHED_TIME_RANGE_TYPE', type)
      } else {
        this.$store.commit('yqmonitorMenu/SET_CACHED_TIME_RANGE_TYPE', null)
      }
    },
    initForm() {
      const lastQueryParams = this.$store.state.yqmonitorMenu.lastQueryParams
      if (lastQueryParams && Object.keys(lastQueryParams).length > 0) {
        if (
          this.cachedTimeRangeType &&
          this.cachedTimeRangeType !== 'all' &&
          this.cachedTimeRangeType !== 'custom'
        ) {
          this.selectTimeRange(this.cachedTimeRangeType)
        } else if (!lastQueryParams.startTime || !lastQueryParams.endTime) {
          this.timeRangeType = 'all'
          this.dateRange = []
        } else {
          this.timeRangeType = 'custom'
          this.dateRange = [lastQueryParams.startTime, lastQueryParams.endTime]
        }
        this.form.startTime = lastQueryParams.startTime || ''
        this.form.endTime = lastQueryParams.endTime || ''
        this.sensitivitysUI =
          lastQueryParams.sensitivitys?.length === 0
            ? ['all']
            : lastQueryParams.sensitivitys || ['all']
        this.form.sensitivitys = lastQueryParams.sensitivitys || []
        this.aiSentimentTypesUI =
          lastQueryParams.aiSentimentTypes?.length === 0
            ? ['all']
            : lastQueryParams.aiSentimentTypes || ['all']
        this.form.aiSentimentTypes = lastQueryParams.aiSentimentTypes || []
        this.publishTypesUI =
          lastQueryParams.publishTypes?.length === 0
            ? ['all']
            : lastQueryParams.publishTypes || ['all']
        this.form.publishTypes = lastQueryParams.publishTypes || []
        this.accountAuthTypesUI =
          lastQueryParams.accountAuthTypes?.length === 0
            ? ['all']
            : lastQueryParams.accountAuthTypes || ['all']
        this.form.accountAuthTypes = lastQueryParams.accountAuthTypes || []
        this.platformsUI =
          lastQueryParams.platforms?.length === 0
            ? ['all']
            : lastQueryParams.platforms || ['all']
        this.form.platforms = lastQueryParams.platforms || []
        this.negativeSentimentTagsUI =
          lastQueryParams.negativeSentimentTags?.length === 0
            ? ['all']
            : lastQueryParams.negativeSentimentTags || ['all']
        this.form.negativeSentimentTags =
          lastQueryParams.negativeSentimentTags || []
        this.sourceLevelsUI =
          lastQueryParams.sourceLevels?.length === 0
            ? ['all']
            : lastQueryParams.sourceLevels || ['all']
        this.form.sourceLevels = lastQueryParams.sourceLevels || []
        const currentReadStatusValue = lastQueryParams[this.readStatusKey];
        this.readStatusUI = currentReadStatusValue && currentReadStatusValue.length > 0
          ? currentReadStatusValue[0]
          : 'all';
        this.form.readStatus = lastQueryParams.readStatus || []
        this.noiseUI =
          lastQueryParams.noiseTypes?.length === 0
            ? 'all'
            : lastQueryParams.noiseTypes?.[0] || 'all'
        this.form.noiseTypes = lastQueryParams.noiseTypes || []
        this.institutionOpenFlagUI =
          lastQueryParams.institutionOpenFlags?.length === 0
            ? 'all'
            : lastQueryParams.institutionOpenFlags?.[0] || 'all'
        this.form.institutionOpenFlags =
          lastQueryParams.institutionOpenFlags || []
        this.similarUI = lastQueryParams.enableSimilarityDedup || 'fold'
        this.form.enableSimilarityDedup =
          lastQueryParams.enableSimilarityDedup || 'fold'
        let matched = false
        if (!lastQueryParams.minFansCount && !lastQueryParams.maxFansCount) {
          this.fansCountSelected = 'all'
          this.fansCountCustom = false
          this.form.minFansCount = 0
          this.form.maxFansCount = 0
        } else {
          for (const opt of this.fansCountOptions) {
            if (
              Number(lastQueryParams.minFansCount) === Number(opt.min) &&
              (opt.max === ''
                ? lastQueryParams.maxFansCount === '' ||
                lastQueryParams.maxFansCount === null
                : Number(lastQueryParams.maxFansCount) === Number(opt.max))
            ) {
              this.fansCountSelected = opt.value
              this.fansCountCustom = false
              matched = true
              break
            }
          }
          if (!matched) {
            this.fansCountSelected = ''
            this.fansCountCustom = true
            this.form.minFansCount = lastQueryParams.minFansCount || 0
            this.form.maxFansCount = lastQueryParams.maxFansCount || 0
          }
        }
        // 初始化必含词组
        const mustContainGroupsVal = lastQueryParams.mustContainGroups || []
        if (!mustContainGroupsVal || mustContainGroupsVal.length === 0) {
          this.form.mustContainGroups = [{ id: Date.now(), value: '' }]
        } else {
          this.form.mustContainGroups = mustContainGroupsVal.map(
            (value, index) => ({ id: Date.now() + index, value })
          )
        }

        // 初始化排除词组
        const mustNotContainGroupsVal = lastQueryParams.mustNotContainGroups || []
        if (!mustNotContainGroupsVal || mustNotContainGroupsVal.length === 0) {
          this.form.mustNotContainGroups = [{ id: Date.now() + 1, value: '' }]
        } else {
          this.form.mustNotContainGroups = mustNotContainGroupsVal.map(
            (value, index) => ({ id: Date.now() + index + 1000, value })
          )
        }

        this.form.shouldContain = lastQueryParams.shouldContain || ''
        this.form.titleMustNotContain = lastQueryParams.titleMustNotContain || ''
      } else {
        this.resetForm()
      }
    },
    initFormFromJson(jsonStr) {
      let data
      try {
        data = JSON.parse(jsonStr)
      } catch (e) {
        this.$message.error('数据格式错误，无法解析')
        return
      }
      this.form = {
        ...this.form,
        ...data,
        sensitivitys: data.sensitivitys || [],
        aiSentimentTypes: data.aiSentimentTypes || [],
        publishTypes: data.publishTypes || [],
        accountAuthTypes: data.accountAuthTypes || [],
        platforms: data.platforms || [],
        negativeSentimentTags: data.negativeSentimentTags || [],
        sourceLevels: data.sourceLevels || [],
        noiseTypes: data.noiseTypes || [],
        readStatus: data.readStatus || [],
        institutionOpenFlags: data.institutionOpenFlags || [],
        enableSimilarityDedup: data.enableSimilarityDedup || 'fold'
      }
      const readStatusKeys = {
        readStatus: 'yqmonitor',
        followReadStatus: 'followedEvents',
        targetDetectReadStatus: 'targetMonitor'
      };
      let foundKey = 'readStatus';
      for (const key in readStatusKeys) {
        if (data[key] && data[key].length > 0) {
          foundKey = key;
          break;
        }
      }
      this.readStatusUI = data[foundKey] && data[foundKey].length > 0 ? data[foundKey][0] : 'all';
      if (data.mustContainGroups && data.mustContainGroups.length > 0) {
        this.form.mustContainGroups = data.mustContainGroups.map(
          (g, index) => ({
            id: g.id || Date.now() + index,
            value: g.value || ''
          })
        )
      } else if (data.mustContain) {
        this.form.mustContainGroups = [{ id: Date.now(), value: data.mustContain }]
      } else {
        this.form.mustContainGroups = [{ id: Date.now(), value: '' }]
      }

      if (data.mustNotContainGroups && data.mustNotContainGroups.length > 0) {
        this.form.mustNotContainGroups = data.mustNotContainGroups.map(
          (g, index) => ({
            id: g.id || Date.now() + index + 1000,
            value: g.value || ''
          })
        )
      } else if (data.mustNotContain) {
        this.form.mustNotContainGroups = [{ id: Date.now() + 1, value: data.mustNotContain }]
      } else {
        this.form.mustNotContainGroups = [{ id: Date.now() + 1, value: '' }]
      }

      this.form.shouldContain = data.shouldContain || ''
      this.form.titleMustNotContain = data.titleMustNotContain || ''

      if (
        data.cachedTimeRangeType &&
        data.cachedTimeRangeType !== 'all' &&
        data.cachedTimeRangeType !== 'custom'
      ) {
        this.selectTimeRange(data.cachedTimeRangeType)
      } else if (this.form.startTime && this.form.endTime) {
        this.dateRange = [this.form.startTime, this.form.endTime]
        this.timeRangeType = 'custom'
      } else {
        this.dateRange = []
        this.timeRangeType = 'all'
      }
      delete this.form.cachedTimeRangeType
      this.sensitivitysUI =
        this.form.sensitivitys.length === 0 ? ['all'] : this.form.sensitivitys
      this.aiSentimentTypesUI =
        this.form.aiSentimentTypes.length === 0
          ? ['all']
          : this.form.aiSentimentTypes
      this.publishTypesUI =
        this.form.publishTypes.length === 0 ? ['all'] : this.form.publishTypes
      this.accountAuthTypesUI =
        this.form.accountAuthTypes.length === 0
          ? ['all']
          : this.form.accountAuthTypes
      this.platformsUI =
        this.form.platforms.length === 0 ? ['all'] : this.form.platforms
      this.negativeSentimentTagsUI =
        this.form.negativeSentimentTags.length === 0
          ? ['all']
          : this.form.negativeSentimentTags
      this.sourceLevelsUI =
        this.form.sourceLevels.length === 0 ? ['all'] : this.form.sourceLevels
      this.noiseUI =
        this.form.noiseTypes.length === 0 ? 'all' : this.form.noiseTypes[0]
      this.institutionOpenFlagUI =
        this.form.institutionOpenFlags.length === 0
          ? 'all'
          : this.form.institutionOpenFlags[0]
      // this.readStatusUI =
      //   this.form.readStatus.length === 0 ? 'all' : this.form.readStatus[0]
      this.similarUI = this.form.enableSimilarityDedup
      let matched = false
      if (!this.form.minFansCount && !this.form.maxFansCount) {
        this.fansCountSelected = 'all'
        this.fansCountCustom = false
      } else {
        for (const opt of this.fansCountOptions) {
          if (
            Number(this.form.minFansCount) === Number(opt.min) &&
            (opt.max === ''
              ? this.form.maxFansCount === '' || this.form.maxFansCount === null
              : Number(this.form.maxFansCount) === Number(opt.max))
          ) {
            this.fansCountSelected = opt.value
            this.fansCountCustom = false
            matched = true
            break
          }
        }
        if (!matched) {
          this.fansCountSelected = ''
          this.fansCountCustom = true
        }
      }
    },
    addMustContainGroup() {
      this.form.mustContainGroups.push({ id: Date.now(), value: '' })
    },
    removeMustContainGroup(id) {
      const index = this.form.mustContainGroups.findIndex((g) => g.id === id)
      if (index !== -1) {
        this.form.mustContainGroups.splice(index, 1)
      }
    },
    addMustNotContainGroup() {
      this.form.mustNotContainGroups.push({ id: Date.now(), value: '' })
    },
    removeMustNotContainGroup(id) {
      const index = this.form.mustNotContainGroups.findIndex((g) => g.id === id)
      if (index !== -1) {
        this.form.mustNotContainGroups.splice(index, 1)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.filter-drawer-footer {
  position: fixed;
  right: 0;
  bottom: 0;
  width: 800px;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  padding: 16px 40px;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  z-index: 100;
}

.btn-reset {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #d9d9d9;
}

.btn-save {
  background: #fff7e6;
  color: #faad14;
  border: 1px solid #faad14;
}

::v-deep .el-drawer__body {
  padding-bottom: 100px;
}

.keyword-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.keyword-group:last-child {
  margin-bottom: 0;
}

.keyword-group-btn {
  margin-left: 8px;
}
</style>
```

## File: src/views/yqmonitor/components/MainContent/index.vue
```vue
<template>
  <div class="main-content">
    <div v-loading="loading || similarArticlesLoading" class="card-box">
      <div class="fixed-header">
        <TopNav
          ref="topNavRef"
          :filter-tags="localFilterTags"
          :max-visible-tags="maxVisibleTags"
          :module-type="moduleType"
          @tagClick="handleTagClick"
          @nav-change="handleNavChange"
          @filterOpen="handleFilterOpen"
          @refresh="getContentList"
          @refreshTags="handleResetAndRefresh"
          @resetFilter="handleFilterReset"
          @search="handleSearch"
          @clear-search="handleClearSearch"
          @tags-config-changed="handleTagsOrderChanged"
        />
      </div>
      <div ref="scrollableContentRef" class="scrollable-content">
        <ContentList
          :list="contentList"
          :page-num="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          :total="total"
          :checked-list="checkedList"
          :sentiment-options="sentimentOptions"
          :ai-sentiment-options="aiSentimentOptions"
          :module-type="moduleType"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @check-change="handleCheckChange"
          @delete="handleSingleDelete"
          @change-sentiment="handleChangeSingleSentiment"
          @change-ai-sentiment="handleChangeSingleAiSentimentType"
          @mark-read="handleSingleMarkRead"
          @expand-similar="handleExpandSimilarArticles"
          @toggle-favorite="handleToggleFavorite"
        />
        <ScrollButtons :scroll-container="$refs.scrollableContentRef" />
      </div>
      <FixedActionBar
        :checked-list="checkedList"
        :content-list="contentList"
        :sentiment-options="sentimentOptions"
        :ai-sentiment-options="aiSentimentOptions"
        :selected-sort-type="selectedSortType"
        :sort-options="sortOptions"
        :module-type="moduleType"
        :is-batch-unfavorite-mode="isBatchUnfavoriteMode"
        @check-all="handleCheckAll"
        @mark-read="handleMarkRead"
        @change-sentiment="handleChangeSentiment"
        @change-ai-sentiment-type="handleChangeAiSentimentType"
        @sort-type-change="handleSortTypeChange"
        @delete="handleDelete"
        @refresh="getContentList"
        @batch-favorite="handleBatchFavorite"
      />
      <FilterDrawer
        ref="filterDrawerRef"
        :visible.sync="filterVisible"
        :editing-tag-id="editingTagId"
        :module-type="moduleType"
        @submit="onFilterSubmit"
        @submitAndSave="onFilterSubmitAndSave"
        @updateFilterWord="handleUpdateFilterWord"
      />
      <el-dialog
        title="保存条件"
        :visible.sync="saveDialogVisible"
        width="400px"
        @close="saveDialogVisible = false"
      >
        <el-input v-model="saveName" placeholder="请输入条件名称" />
        <span slot="footer" class="dialog-footer">
          <el-button @click="saveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveConfirm">确定</el-button>
        </span>
      </el-dialog>
      <SimilarArticlesDialog
        :visible.sync="similarDialogVisible"
        :articles="similarArticlesList"
        :loading="similarArticlesLoading"
        :representative-article-title="currentRepresentativeArticleTitle"
        :total="similarArticlesTotal"
        @page-change="handleSimilarArticlesPageChange"
      />
    </div>
  </div>
</template>
<script>
import {
  getPublicSentimentList,
  getFavoritePublicSentimentList,
  getFilterWordList,
  addFilterWord,
  updateFilterWord,
  batchUpdatePublicSentiment,
  batchUpdateSentiment,
  batchUpdateAiSentimentType,
  batchDeletePublicSentiment,
  batchUpdateFavoriteStatus,
  getSimilarGroupListApi,
} from "@/api/yqmonitor";
import TopNav from "./TopNav.vue";
import ContentList from "./ContentList.vue";
import FilterDrawer from "./FilterDrawer.vue";
import FixedActionBar from "./FixedActionBar.vue";
import { getDateRangeByType } from "@/utils/yqmonitorTool";
import ScrollButtons from "@/views/yqmonitor/components/scrollButtons.vue";
import SimilarArticlesDialog from "./SimilarArticlesDialog.vue";
import { mapState } from "vuex";

export default {
  name: "MainContent",
  components: {
    TopNav,
    ContentList,
    FilterDrawer,
    FixedActionBar,
    ScrollButtons,
    SimilarArticlesDialog,
  },
  dicts: ["filter_sentiment", "ai_public_opinion_type"],
  props: {
    moduleType: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      contentList: [],
      loading: false,
      total: 0,
      maxVisibleTags: 20,
      queryParams: {
        pageNum: 1,
        pageSize: 30,
        mediaTypes: [],
        isAsc: "desc",
        orderByColumn: "",
        enableSimilarityDedup: "fold",
      },
      filterVisible: false,
      filterTags: [],
      localFilterTags: [],
      saveDialogVisible: false,
      saveName: "",
      saveForm: null,
      checkedList: [],
      selectedSortType: "time_desc",
      sortOptions: [
        {
          label: "按时间降序",
          value: "time_desc",
          orderByColumn: "pushTime",
          isAsc: "desc",
        },
        {
          label: "按时间升序",
          value: "time_asc",
          orderByColumn: "pushTime",
          isAsc: "asc",
        },
        {
          label: "评论数",
          value: "comment_desc",
          orderByColumn: "commentCount",
          isAsc: "desc",
        },
        {
          label: "转发数",
          value: "repost_desc",
          orderByColumn: "repostCount",
          isAsc: "desc",
        },
        {
          label: "点赞数",
          value: "like_desc",
          orderByColumn: "likeCount",
          isAsc: "desc",
        },
        {
          label: "阅读数",
          value: "view_desc",
          orderByColumn: "viewCount",
          isAsc: "desc",
        },
        {
          label: "粉丝数",
          value: "fans_desc",
          orderByColumn: "fansCount",
          isAsc: "desc",
        },
      ],
      sentimentOptions: [],
      aiSentimentOptions: [],
      editingTagId: "", // 当前编辑的tag id
      isTestMode: false,
      similarDialogVisible: false,
      similarArticlesList: [],
      similarArticlesLoading: false,
      currentRepresentativeArticleTitle: "相似文章列表",
      // 相似文章分页相关数据
      similarArticlesTotal: 0,
      currentSimilarGroupId: null,
      currentRepresentativeArticle: null,
    };
  },
  computed: {
    ...mapState({
      yqmonitorCurrentNode: (state) => state.yqmonitorMenu.currentNode,
      yqclientCurrentNode: (state) => state.yqclientInstitution.currentNode,
      yqclientIncludeSubInstitutions: (state) =>
        state.yqclientInstitution.includeSubInstitutions,
    }),
    currentNode() {
      if (this.moduleType === "yqclient") {
        return this.yqclientCurrentNode;
      }
      return this.yqmonitorCurrentNode;
    },
    userId() {
      return this.$store.state.user?.id || 1;
    },
    readStatusKey() {
      const keyMap = {
        yqmonitor: "readStatus",
        followedEvents: "followReadStatus",
        targetMonitor: "targetDetectReadStatus",
        yqclient: "readStatus", // 假设客户模块也用这个字段
      };
      return keyMap[this.moduleType] || "readStatus";
    },
    // 判断是否为批量取消收藏模式
    isBatchUnfavoriteMode() {
      // 必须有勾选内容
      if (this.checkedList.length === 0) {
        return false;
      }
      // 找到所有被勾选的文章对象
      const selectedArticles = this.contentList.filter((item) =>
        this.checkedList.includes(item.uniqueId)
      );
      // 只有当所有被勾选的文章的 isFavorited 都为 true 时，才进入“取消收藏”模式
      return (
        selectedArticles.length > 0 &&
        selectedArticles.every((item) => item.isFavorited)
      );
    },
  },
  watch: {
    yqmonitorCurrentNode: {
      handler(newVal, oldVal) {
        // 增加对 moduleType 的判断，防止收藏页因 currentNode 变化而刷新
        if (this.moduleType === "yqclient") return;
        if (
          this.moduleType !== "favorite" &&
          newVal &&
          newVal.id !== oldVal?.id
        ) {
          // 添加路由判断逻辑
          const currentRoute = this.$route.path;
          let shouldExecute = false;
          if (newVal.name === "本地监测") {
            shouldExecute = currentRoute === "/yqmonitor/index";
          } else if (newVal.name === "本地监测(F)") {
            shouldExecute = currentRoute === "/followedEvents/index";
          } else if (newVal.name === "本地监测(T)") {
            shouldExecute = currentRoute === "/targetMonitor/index";
          } else {
            shouldExecute = true;
          }
          if (shouldExecute) {
            this.handleResetAndRefresh();
          }
        } else if (this.moduleType === "favorite" && oldVal === undefined) {
          // 收藏页首次加载
          this.handleResetAndRefresh();
        }
      },
      immediate: true,
    },
    yqclientCurrentNode: {
      handler(newVal, oldVal) {
        if (
          this.moduleType === "yqclient" &&
          newVal &&
          newVal.id !== oldVal?.id
        ) {
          this.handleResetAndRefresh();
        }
      },
      deep: true,
    },
    yqclientIncludeSubInstitutions: {
      handler(newVal, oldVal) {
        if (this.moduleType === "yqclient" && newVal !== oldVal) {
          this.getContentList();
        }
      },
    },
  },
  mounted() {
    this.sentimentOptions = this.dict.type.filter_sentiment || [];
    this.aiSentimentOptions = this.dict.type.ai_public_opinion_type || [];
  },
  methods: {
    collectPlanIds(node, result = []) {
      if (!node) return result;
      if (node.planId) {
        result.push(node.planId);
      }
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => this.collectPlanIds(child, result));
      }
      return result.length > 0 ? result : ["-1"];
    },
    collectInstitutionIds(node, includeChildren) {
      if (!node) return [];
      let ids = [node.id];
      if (includeChildren && node.children && node.children.length > 0) {
        node.children.forEach((child) => {
          ids = ids.concat(this.collectInstitutionIds(child, true));
        });
      }
      return ids;
    },
    async getContentList() {
      try {
        this.loading = true;
        // 收藏页不需要currentNode，但其他页面需要，做个前置检查
        if (this.moduleType !== "favorite" && !this.currentNode) {
          this.loading = false;
          return;
        }

        const handleTimeRange = (type) => {
          if (type) {
            const [startTime, endTime] = getDateRangeByType(type);
            if (startTime && endTime) {
              this.queryParams = {
                ...this.queryParams,
                startTime,
                endTime,
              };
            }
          }
        };
        if (this.queryParams.cachedTimeRangeType) {
          handleTimeRange(this.queryParams.cachedTimeRangeType);
        } else if (this.$store.state.yqmonitorMenu.cachedTimeRangeType) {
          handleTimeRange(this.$store.state.yqmonitorMenu.cachedTimeRangeType);
        }

        let params = {};
        let apiFunc;

        if (this.moduleType === "favorite") {
          // [新增] 收藏页的逻辑
          apiFunc = getFavoritePublicSentimentList;
          params = {
            favoriteStatus: 1,
            favoriteUserId: this.userId,
            ...this.queryParams,
          };
        } else if (this.moduleType === "yqclient") {
          // yqclient 模块逻辑
          apiFunc = getPublicSentimentList;
          const institutionCodes = this.collectInstitutionIds(
            this.currentNode,
            this.yqclientIncludeSubInstitutions
          );
          params = {
            ...this.queryParams,
            institutionCodes:
              institutionCodes.length > 0 ? institutionCodes : ["-1"],
          };
        } else {
          // 原有逻辑
          apiFunc = getPublicSentimentList;
          params = {
            schemeNumbers: this.collectPlanIds(this.currentNode),
            ...this.queryParams,
          };
        }

        if (this.moduleType !== "yqclient") {
          this.$store.commit("yqmonitorMenu/SET_LAST_QUERY_PARAMS", params);
        }
        this.checkedList = [];

        // 使用动态选择的API函数
        const { rows, total } = await apiFunc(params);
        this.contentList = rows.map((row) => {
          // favoriteStatus 字段需要后端返回
          return {
            ...row,
            moduleReadStatus: row[this.readStatusKey],
            isFavorited: row.favoriteStatus === 1,
          };
        });
        this.total = total;
        this.resetScrollPosition();
      } catch (error) {
        console.error("获取内容列表失败:", error);
        this.$message.error("获取内容列表失败");
      } finally {
        this.loading = false;
      }
    },
    onFilterSubmit(form) {
      if ("cachedTimeRangeType" in this.queryParams) {
        delete this.queryParams.cachedTimeRangeType;
      }
      this.queryParams = {
        ...this.queryParams,
        ...form,
        pageNum: 1,
      };
      this.getContentList();
      this.$refs.topNavRef.resetSelectedTag();
    },
    handleNavChange({ mediaTypes }) {
      if (
        !mediaTypes ||
        (Array.isArray(mediaTypes) && mediaTypes.length === 0) ||
        (Array.isArray(mediaTypes) &&
          mediaTypes.length === 1 &&
          mediaTypes[0] === "all")
      ) {
        this.queryParams.mediaTypes = [];
      } else {
        this.queryParams.mediaTypes = mediaTypes;
      }
      this.queryParams.pageNum = 1;
      this.getContentList();
    },
    handleFilterOpen(filterObj) {
      this.filterVisible = true;
      this.editingTagId = filterObj?.editingTagId || "";
      // 如果有传入的筛选参数，则初始化表单
      if (filterObj) {
        if (this.moduleType !== "yqclient") {
          this.$store.commit("yqmonitorMenu/SET_OPENING_FROM_TAG", true);
        }
        this.$nextTick(() => {
          this.$refs.filterDrawerRef.initFormFromJson(filterObj.paramJson);
        });
      } else {
        if (this.moduleType !== "yqclient") {
          this.$store.commit("yqmonitorMenu/SET_OPENING_FROM_TAG", false);
        }
      }
    },
    handlePageChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getContentList();
    },
    handleSizeChange(pageSize) {
      this.queryParams.pageSize = pageSize;
      this.getContentList();
    },
    resetScrollPosition() {
      this.$nextTick(() => {
        if (this.$refs.scrollableContentRef) {
          this.$refs.scrollableContentRef.scrollTop = 0;
        }
      });
    },
    async fetchTags() {
      if (this.moduleType === "favorite") {
        // 收藏模式不需要获取标签
        return;
      }
      if (this.isTestMode) {
        const mockTags = this.generateMockTags(25);
        const userPreferences = this.loadUserTagPreferences();
        const processedTags = mockTags.map((mockTag, index) => {
          const preference = userPreferences.find((p) => p.id === mockTag.id);
          return {
            ...mockTag,
            isPinned: preference
              ? preference.isPinned
              : index < this.maxVisibleTags,
            order: preference ? preference.order : mockTag.originalOrder,
          };
        });
        processedTags.sort((a, b) => {
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          return a.order - b.order;
        });
        let currentOrder = 0;
        const pinnedTags = processedTags
          .filter((t) => t.isPinned)
          .sort((a, b) => a.order - b.order);
        const unpinnedTags = processedTags
          .filter((t) => !t.isPinned)
          .sort((a, b) => a.order - b.order);
        const finalSortedTags = [];
        pinnedTags.forEach((tag) => {
          finalSortedTags.push({ ...tag, order: currentOrder++ });
        });
        unpinnedTags.forEach((tag) => {
          finalSortedTags.push({ ...tag, order: currentOrder++ });
        });
        this.filterTags = finalSortedTags;
        this.localFilterTags = finalSortedTags;
        console.log("Test mode tags:", this.filterTags);
        return;
      }
      try {
        if (!this.currentNode) return;
        if (
          this.moduleType !== "yqclient" &&
          typeof this.currentNode.id === "string" &&
          this.currentNode.id.startsWith("category_")
        ) {
          return;
        }
        const res = await getFilterWordList({
          userId: this.userId,
          menuId: this.currentNode.id,
          pageNum: 1,
          pageSize: 100,
        });
        const apiTags = (res.rows || []).map((tag) => ({
          ...tag,
          id: `${tag.userId}${tag.menuId}${tag.name}`,
          originalOrder: tag.order || 0,
        }));
        const userPreferences = this.loadUserTagPreferences();
        const processedTags = apiTags.map((apiTag, index) => {
          const preference = userPreferences.find((p) => p.id === apiTag.id);
          return {
            ...apiTag,
            isPinned: preference
              ? preference.isPinned
              : index < this.maxVisibleTags,
            order: preference ? preference.order : apiTag.originalOrder,
          };
        });
        processedTags.sort((a, b) => {
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;
          return a.order - b.order;
        });
        let currentOrder = 0;
        const pinnedTags = processedTags
          .filter((t) => t.isPinned)
          .sort((a, b) => a.order - b.order);
        const unpinnedTags = processedTags
          .filter((t) => !t.isPinned)
          .sort((a, b) => a.order - b.order);
        const finalSortedTags = [];
        pinnedTags.forEach((tag) => {
          finalSortedTags.push({ ...tag, order: currentOrder++ });
        });
        unpinnedTags.forEach((tag) => {
          finalSortedTags.push({ ...tag, order: currentOrder++ });
        });
        this.filterTags = finalSortedTags;
        this.localFilterTags = finalSortedTags;
        console.log("filterTags:", this.filterTags);
      } catch (error) {
        console.error("获取标签列表失败:", error);
        this.$message.error("获取标签列表失败");
      }
    },
    generateMockTags(count) {
      const tags = [];
      const adjectives = [
        "重要",
        "紧急",
        "普通",
        "特殊",
        "关键",
        "核心",
        "基础",
        "高级",
        "临时",
        "长期",
      ];
      const nouns = [
        "监控",
        "分析",
        "预警",
        "报告",
        "统计",
        "评估",
        "追踪",
        "调查",
        "研究",
        "测试",
      ];
      for (let i = 0; i < count; i++) {
        const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
        const noun = nouns[Math.floor(Math.random() * nouns.length)];
        const name = `${adj}${noun}${i + 1}`;
        tags.push({
          id: `test_${i}`,
          name: name,
          userId: 1,
          menuId: 1,
          originalOrder: i,
          isPinned: i < 3,
          paramJson: JSON.stringify({
            mediaTypes: ["all"],
            sensitivitys: ["all"],
          }),
        });
      }
      return tags;
    },
    async onFilterSubmitAndSave(form) {
      this.saveForm = form;
      this.saveDialogVisible = true;
      this.saveName = "";
      // 更新查询参数以立即应用筛选
      const apiReadyForm = { ...form };
      if (
        apiReadyForm.mustContainGroups &&
        Array.isArray(apiReadyForm.mustContainGroups)
      ) {
        apiReadyForm.mustContainGroups = apiReadyForm.mustContainGroups
          .map((g) => g.value.trim())
          .filter(Boolean);
      }
      if (
        apiReadyForm.mustNotContainGroups &&
        Array.isArray(apiReadyForm.mustNotContainGroups)
      ) {
        apiReadyForm.mustNotContainGroups = apiReadyForm.mustNotContainGroups
          .map((g) => g.value.trim())
          .filter(Boolean);
      }
      this.queryParams = {
        ...this.queryParams,
        ...apiReadyForm,
        pageNum: 1,
      };
      this.getContentList();
    },
    // handleSaveConfirm只保留addFilterWord逻辑
    async handleSaveConfirm() {
      if (!this.saveName) {
        this.$message.warning("请输入条件名称");
        return;
      }
      if (!this.currentNode) {
        this.$message.error("无法保存，未选择任何节点");
        return;
      }
      const cachedTimeRangeType =
        this.$store.state.yqmonitorMenu.cachedTimeRangeType;

      // 新增标签
      await addFilterWord({
        userId: this.userId,
        menuId: this.currentNode.id,
        name: this.saveName,
        paramJson: JSON.stringify({
          ...this.saveForm,
          ...(cachedTimeRangeType && { cachedTimeRangeType }),
        }),
      });
      this.$message.success("保存成功");
      this.saveDialogVisible = false;
      await this.fetchTags();
      // 设置新标签为选中状态
      const newTagId = `${this.userId}${this.currentNode.id}${this.saveName}`;
      this.$refs.topNavRef.setSelectedTag(newTagId);
    },
    // 添加处理更新筛选条件的方法
    async handleUpdateFilterWord({ id, filterWord }) {
      try {
        if (!this.currentNode) {
          this.$message.error("无法更新，未选择任何节点");
          return;
        }
        const tag = this.filterTags.find((tag) => tag.id === id);
        const tagName = tag ? tag.name : "";
        if (!tagName) {
          this.$message.error("未找到对应的标签名");
          return;
        }
        const cachedTimeRangeType =
          this.$store.state.yqmonitorMenu.cachedTimeRangeType;
        await updateFilterWord({
          userId: this.userId,
          menuId: this.currentNode.id,
          name: tagName,
          paramJson: JSON.stringify({
            ...filterWord,
            ...(cachedTimeRangeType && { cachedTimeRangeType }),
          }),
        });
        // 更新查询参数
        const apiReadyFilterWord = { ...filterWord };
        if (
          apiReadyFilterWord.mustContainGroups &&
          Array.isArray(apiReadyFilterWord.mustContainGroups)
        ) {
          apiReadyFilterWord.mustContainGroups =
            apiReadyFilterWord.mustContainGroups
              .map((g) => g.value.trim())
              .filter(Boolean);
        }
        if (
          apiReadyFilterWord.mustNotContainGroups &&
          Array.isArray(apiReadyFilterWord.mustNotContainGroups)
        ) {
          apiReadyFilterWord.mustNotContainGroups =
            apiReadyFilterWord.mustNotContainGroups
              .map((g) => g.value.trim())
              .filter(Boolean);
        }
        this.queryParams = {
          ...this.queryParams,
          ...apiReadyFilterWord,
          pageNum: 1,
        };
        // 获取内容列表
        await this.getContentList();
        // 更新标签列表
        await this.fetchTags();
        // 清理LocalStorage中可能存在的该标签的排序信息
        const prefs = this.loadUserTagPreferences();
        const updatedPrefs = prefs.filter((p) => p.id !== id);
        this.saveUserTagPreferences(updatedPrefs);
        this.$message.success("更新成功");
        this.editingTagId = "";
      } catch (error) {
        console.error("更新筛选条件失败:", error);
        this.$message.error("更新筛选条件失败");
      }
    },
    handleTagClick(tag) {
      try {
        const params = JSON.parse(tag.paramJson);
        // 将保存的词组格式转换为API格式
        if (
          params.mustContainGroups &&
          Array.isArray(params.mustContainGroups)
        ) {
          params.mustContainGroups = params.mustContainGroups
            .map((g) => g.value.trim())
            .filter(Boolean);
        } else if (params.mustContain) {
          // 兼容旧版
          params.mustContainGroups = [params.mustContain.trim()].filter(
            Boolean
          );
          delete params.mustContain;
        }
        if (
          params.mustNotContainGroups &&
          Array.isArray(params.mustNotContainGroups)
        ) {
          params.mustNotContainGroups = params.mustNotContainGroups
            .map((g) => g.value.trim())
            .filter(Boolean);
        } else if (params.mustNotContain) {
          // 兼容旧版
          params.mustNotContainGroups = [params.mustNotContain.trim()].filter(
            Boolean
          );
          delete params.mustNotContain;
        }
        this.queryParams = {
          // 1. 保留那些不受筛选条件影响的、由其他组件控制的核心参数
          pageSize: this.queryParams.pageSize,
          mediaTypes: this.queryParams.mediaTypes, // 由顶部导航控制
          isAsc: this.queryParams.isAsc, // 由排序组件控制
          orderByColumn: this.queryParams.orderByColumn, // 由排序组件控制
          // 2. 应用从标签解析出来的所有筛选参数
          ...params,
          // 3. 确保翻页和搜索等状态被重置或设置
          pageNum: 1,
          searchText: this.queryParams.searchText, // 保留搜索词
          searchFields: this.queryParams.searchFields,
        };
        this.getContentList();
      } catch (e) {
        this.$message.error("条件解析失败");
      }
    },
    handleCheckChange({ id, checked }) {
      if (checked) {
        if (!this.checkedList.includes(id)) {
          this.checkedList.push(id);
        }
      } else {
        this.checkedList = this.checkedList.filter((_id) => _id !== id);
      }
    },
    handleCheckAll(val) {
      if (val) {
        this.checkedList = this.contentList.map((item) => item.uniqueId);
      } else {
        this.checkedList = [];
      }
    },
    handleSortTypeChange(value) {
      const option = this.sortOptions.find((opt) => opt.value === value);
      if (option) {
        this.queryParams.orderByColumn = option.orderByColumn;
        this.queryParams.isAsc = option.isAsc;
        this.queryParams.pageNum = 1;
        this.getContentList();
      }
    },
    handleFilterReset(needGetContentList = false) {
      // 重置选中的标签
      //  const readStatusKeys = ['readStatus', 'followReadStatus', 'targetDetectReadStatus'];
      this.queryParams = {
        pageNum: 1,
        pageSize: 30,
        mediaTypes: [],
        isAsc: "desc",
        orderByColumn: "",
        enableSimilarityDedup: "fold",
      };
      if (this.$refs.topNavRef) {
        this.$refs.topNavRef.resetSelectedTag();
        this.$refs.topNavRef.resetActiveNav();
        this.$refs.topNavRef.handleExitSearchBar("no-emit");
      }
      if (needGetContentList) {
        this.getContentList();
      }
    },
    async handleResetAndRefresh() {
      this.handleFilterReset();
      await this.getContentList();
      await this.fetchTags();
    },
    async handleMarkRead() {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要标记为已读的内容");
        return;
      }
      try {
        const payload = {
          uniqueIds: this.checkedList,
          [this.readStatusKey]: 1,
        };
        await batchUpdatePublicSentiment(payload);
        this.contentList = this.contentList.map((item) => {
          if (this.checkedList.includes(item.uniqueId)) {
            return { ...item, moduleReadStatus: 1 };
          }
          return item;
        });
        this.$message.success("标记已读成功");
      } catch (e) {
        this.$message.error("标记已读失败");
      }
    },
    async handleChangeSentiment(value) {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要修改的内容");
        return;
      }
      try {
        await batchUpdateSentiment({
          uniqueIds: this.checkedList,
          sensitivity: value,
        });
        this.contentList = this.contentList.map((item) => {
          if (this.checkedList.includes(item.uniqueId)) {
            return {
              ...item,
              feature: {
                ...item.feature,
                sensitive: value,
              },
            };
          }
          return item;
        });
        this.$message.success("修改倾向性成功");
      } catch (e) {
        this.$message.error("修改倾向性失败");
      }
    },
    async handleChangeAiSentimentType(value) {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要修改的内容");
        return;
      }
      try {
        await batchUpdateAiSentimentType({
          uniqueIds: this.checkedList,
          aiSentimentType: value,
        });
        this.contentList = this.contentList.map((item) => {
          if (this.checkedList.includes(item.uniqueId)) {
            return {
              ...item,
              dmxTagInfo: {
                ...item.dmxTagInfo,
                label: value,
              },
            };
          }
          return item;
        });
        this.$message.success("修改AI舆情倾向性成功");
      } catch (e) {
        this.$message.error("修改AI舆情倾向性失败");
      }
    },
    async handleDelete() {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要删除的内容");
        return;
      }
      try {
        await this.$confirm("确定要删除吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        await batchDeletePublicSentiment({ uniqueIds: this.checkedList });
        this.contentList = this.contentList.filter(
          (item) => !this.checkedList.includes(item.uniqueId)
        );
        this.checkedList = [];
        this.$message.success("删除成功");
      } catch (e) {
        if (e !== "cancel") {
          this.$message.error("删除失败");
        }
      }
    },
    async handleSingleDelete(item) {
      try {
        await batchDeletePublicSentiment({ uniqueIds: [item.uniqueId] });
        this.contentList = this.contentList.filter(
          (i) => i.uniqueId !== item.uniqueId
        );
        if (this.checkedList.includes(item.uniqueId)) {
          this.checkedList = this.checkedList.filter(
            (id) => id !== item.uniqueId
          );
        }
        this.$message.success("删除成功");
      } catch (e) {
        this.$message.error("删除失败");
      }
    },
    async handleChangeSingleSentiment({ id, value }) {
      try {
        await batchUpdateSentiment({
          uniqueIds: [id],
          sensitivity: value.sentiment,
        });
        this.contentList = this.contentList.map((item) => {
          if (item.uniqueId === id) {
            return {
              ...item,
              feature: {
                ...item.feature,
                sensitive: value.sentiment,
              },
            };
          }
          return item;
        });
        this.$message.success("修改倾向性成功");
      } catch (e) {
        this.$message.error("修改倾向性失败");
      }
    },
    async handleChangeSingleAiSentimentType({ id, value }) {
      try {
        await batchUpdateAiSentimentType({
          uniqueIds: [id],
          aiSentimentType: value.value,
        });
        this.contentList = this.contentList.map((item) => {
          if (item.uniqueId === id) {
            return {
              ...item,
              dmxTagInfo: {
                ...item.dmxTagInfo,
                label: value.value,
              },
            };
          }
          return item;
        });
        this.$message.success("修改AI舆情倾向性成功");
      } catch (e) {
        this.$message.error("修改AI舆情倾向性失败");
      }
    },
    async handleSingleMarkRead(uniqueId) {
      try {
        const payload = {
          uniqueIds: [uniqueId],
          [this.readStatusKey]: 1,
        };
        await batchUpdatePublicSentiment(payload);
        this.contentList = this.contentList.map((item) => {
          if (item.uniqueId === uniqueId) {
            return { ...item, moduleReadStatus: 1 };
          }
          return item;
        });
      } catch (e) {
        this.$message.error("标记已读失败");
      }
    },
    handleSearch({ fields, text }) {
      this.queryParams = {
        ...this.queryParams,
        searchFields: fields,
        searchText: text,
        pageNum: 1,
      };
      this.$store.commit("yqmonitorMenu/SET_SEARCH_TEXT", text);
      this.getContentList();
    },
    handleClearSearch() {
      if ("searchFields" in this.queryParams) {
        delete this.queryParams.searchFields;
      }
      if ("searchText" in this.queryParams) {
        delete this.queryParams.searchText;
      }
      this.$store.commit("yqmonitorMenu/SET_SEARCH_TEXT", "");
      this.queryParams.pageNum = 1;
      this.getContentList();
    },
    // 新增：加载用户标签偏好
    loadUserTagPreferences() {
      if (!this.currentNode || !this.currentNode.id) return [];
      const key = `userTagPreferences_${this.userId}_${this.currentNode.id}`;
      const prefs = localStorage.getItem(key);
      try {
        return prefs ? JSON.parse(prefs) : [];
      } catch (e) {
        console.error("Error parsing tag preferences from localStorage", e);
        return [];
      }
    },
    saveUserTagPreferences(tagsToSave) {
      if (!this.currentNode || !this.currentNode.id) return;
      const key = `userTagPreferences_${this.userId}_${this.currentNode.id}`;
      const preferencesToStore = tagsToSave.map((tag) => ({
        id: tag.id,
        isPinned: tag.isPinned,
        order: tag.order,
      }));
      localStorage.setItem(key, JSON.stringify(preferencesToStore));
      console.log("Saved tag preferences:", preferencesToStore);
    },
    handleTagsOrderChanged(newOrderedTags) {
      this.localFilterTags = [...newOrderedTags];
      this.saveUserTagPreferences(newOrderedTags);
      this.$message.success("标签配置已保存");
    },
    async handleExpandSimilarArticles({
      similarGroupId,
      representativeArticle,
    }) {
      if (!similarGroupId) return;
      this.currentSimilarGroupId = similarGroupId;
      this.currentRepresentativeArticle = representativeArticle;
      this.similarDialogVisible = true;
      this.currentRepresentativeArticleTitle =
        representativeArticle?.titleObj?.title || "相似文章列表";
      await this.loadSimilarArticles({
        pageNum: 1,
        pageSize: 10,
      });
    },
    async loadSimilarArticles({ pageNum = 1, pageSize = 10 }) {
      if (!this.currentSimilarGroupId || !this.currentRepresentativeArticle) {
        return;
      }
      this.similarArticlesLoading = true;
      this.similarArticlesList = [];
      try {
        const response = await getSimilarGroupListApi({
          similarGroupId: this.currentSimilarGroupId,
          uniqueId: this.currentRepresentativeArticle.uniqueId,
          pageNum,
          pageSize,
        });
        console.log("response:", response);
        this.similarArticlesList = response.rows || [];
        this.similarArticlesTotal = response.total || 0;
      } catch (error) {
        console.error("获取相似文章失败:", error);
        this.$message.error("获取相似文章列表失败");
        this.similarArticlesList = [];
        this.similarArticlesTotal = 0;
      } finally {
        this.similarArticlesLoading = false;
      }
    },
    async handleSimilarArticlesPageChange({ pageNum, pageSize }) {
      await this.loadSimilarArticles({ pageNum, pageSize });
    },

    // 处理收藏/取消收藏的逻辑
    async handleToggleFavorite({ item, isFavorited }) {
      const newFavoriteStatus = isFavorited ? 1 : 0;
      try {
        await batchUpdateFavoriteStatus({
          uniqueIds: [item.uniqueId],
          favoriteStatus: newFavoriteStatus,
          favoriteUserId: this.userId,
        });
        this.$message.success(isFavorited ? "收藏成功" : "取消收藏成功");

        // 更新UI
        if (this.moduleType === "favorite" && !isFavorited) {
          // 在收藏页取消收藏，直接移除
          this.contentList = this.contentList.filter(
            (i) => i.uniqueId !== item.uniqueId
          );
        } else {
          // 在其他页面，更新状态
          const targetItem = this.contentList.find(
            (i) => i.uniqueId === item.uniqueId
          );
          if (targetItem) {
            targetItem.isFavorited = isFavorited;
          }
        }
      } catch (error) {
        this.$message.error("操作失败");
      }
    },
    // 批量收藏方法
    async handleBatchFavorite() {
      if (this.checkedList.length === 0) {
        this.$message.warning("请先选择要操作的内容");
        return;
      }

      // 根据计算属性决定操作类型和文本
      const isFavoriting = !this.isBatchUnfavoriteMode;
      const status = isFavoriting ? 1 : 0;
      const actionText = isFavoriting ? "收藏" : "取消收藏";

      try {
        await batchUpdateFavoriteStatus({
          uniqueIds: this.checkedList,
          favoriteStatus: status,
        });
        this.$message.success(`批量${actionText}成功`);
        // 更新UI状态
        // 如果是在“收藏”页面执行“取消收藏”，则从列表中移除
        if (this.moduleType === "favorite" && !isFavoriting) {
          this.contentList = this.contentList.filter(
            (item) => !this.checkedList.includes(item.uniqueId)
          );
        } else {
          // 在其他所有情况下（如收藏操作，或在非收藏页取消收藏），仅更新状态
          this.contentList.forEach((item) => {
            if (this.checkedList.includes(item.uniqueId)) {
              item.isFavorited = isFavoriting;
            }
          });
        }

        // 操作成功后，清空勾选列表
        this.checkedList = [];
      } catch (error) {
        this.$message.error(`批量${actionText}失败`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .card-box {
    margin: 20px 20px 0;
    background: white;
    border-radius: 2px 2px 0 0;
    height: calc(100vh - 70px);
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .fixed-header {
    border-bottom: 1px solid #ebeef5;
    padding: 0 20px;
    flex-shrink: 0;
  }
  .scrollable-content {
    overflow-y: auto;
    padding: 20px;
    flex: 1;
    padding-bottom: 90px;
    position: relative;
  }
  .fixed-action-bar {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 16px 20px;
    background: #fff;
    display: flex;
    align-items: center;
    border-top: 1px solid #ebeef5;
    z-index: 10;
    gap: 10px;
  }
}
</style>
```
