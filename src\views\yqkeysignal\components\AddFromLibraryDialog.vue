<template>
  <div>
    <!-- 主对话框：搜索和选择信源 -->
    <el-dialog title="从信源库添加" :visible.sync="mainDialogVisible" width="70%" top="5vh" :close-on-click-modal="false"
      @close="handleMainDialogClose" append-to-body>

      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="平台筛选" prop="platform">
          <el-select v-model="queryParams.platform" placeholder="全部平台" clearable style="width: 150px">
            <el-option v-for="dict in dict.type.content_platform" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="用户ID" prop="user_id">
          <el-input v-model="queryParams.user_id" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜 索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">清空条件</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-check" size="mini" :disabled="selectedSources.length === 0"
            @click="handleOpenClassificationDialog">批量添加选中项</el-button>
        </el-col>
        <el-col>
          <el-alert v-if="selectedSources.length > 0" :title="`已选择 ${selectedSources.length} 项，共找到 ${total} 条结果`"
            type="info" show-icon :closable="false">
          </el-alert>
        </el-col>
      </el-row>

      <!-- 结果表格 -->
      <el-table v-loading="loading" :data="sourceList" @selection-change="handleSelectionChange" ref="searchTable">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="账号昵称" prop="nickname" :show-overflow-tooltip="true" />
        <el-table-column label="用户ID" prop="user_id" :show-overflow-tooltip="true" />
        <el-table-column label="平台用户ID" prop="short_id" :show-overflow-tooltip="true" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.short_id || '--' }}</span>
          </template> 
        </el-table-column>
        <el-table-column label="平台" prop="platform" align="center" width="100" />
        <el-table-column label="二级平台" prop="platform_name" align="center" width="100" />
        <el-table-column label="主页链接" align="center">
          <template slot-scope="scope">
            <a :href="scope.main_domain" target="_blank" class="button-link">查看链接</a>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button v-if="scope.row.is_added !== 1" size="mini" type="text" @click="handleSingleAdd(scope.row)">
              <span v-if="['weibo', 'weixin', 'douyin', 'toutiao', 'xiaohongshu'].includes(scope.row.platform)">
                添加账号
              </span>
              <span v-else>添加站点</span>
            </el-button>
            <span v-else style="color: #c0c4cc; font-size: 12px;">已添加</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.page" :limit.sync="queryParams.page_size"
        @pagination="getList" />
    </el-dialog>

    <!-- 子对话框：配置分类信息 -->
    <el-dialog title="信源分类信息" :visible.sync="classificationDialogVisible" width="600px" append-to-body>

      <el-alert :title="`将为选中的 ${selectedSources.length} 个项目应用这些配置`" type="info" show-icon :closable="false"
        style="margin-bottom: 20px;"></el-alert>

      <el-form ref="classificationForm" :model="classificationForm" label-width="100px">
        <el-form-item label="级别分类" prop="sourceImportance">
          <el-select v-model="classificationForm.sourceImportance" placeholder="请选择级别分类" style="width: 100%;">
            <el-option v-for="dict in dict.type.source_importance" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="网民类型" prop="audienceTypes">
          <el-select v-model="classificationForm.audienceTypes" multiple placeholder="请选择网民类型 (可多选)"
            style="width: 100%;">
            <el-option v-for="dict in dict.type.audience_type" :key="dict.value" :label="dict.label"
              :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属分组" prop="groupIds">
          <el-select v-model="classificationForm.groupIds" multiple placeholder="请选择所属分组 (可多选)" style="width: 100%;">
            <el-option v-for="group in groupList" :key="group.groupId" :label="group.groupName" :value="group.groupId">
              <span style="float: left">{{ group.groupName }}</span>
              <span class="option-color-dot" :style="{ backgroundColor: group.groupColor }"></span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="classificationDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitBatchAdd" :loading="submitLoading">确 认 添 加</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { searchUserFromUniversal } from "@/api/yqkeysignal/universal";
import { batchAddDetail } from "@/api/yqkeysignal/detail";

export default {
  name: "AddFromLibraryDialog",
  dicts: ['content_platform', 'audience_type', 'source_importance'],
  data() {
    return {
      // 主对话框
      mainDialogVisible: false,
      loading: false,
      sourceList: [],
      total: 0,
      queryParams: {
        page: 1,
        page_size: 10,
        platform: undefined,
        keyword: undefined,
        user_id: undefined,
        main_domain: undefined,
        direct_id: null 
      },
      selectedSources: [],

      // 分类对话框
      classificationDialogVisible: false,
      submitLoading: false,
      classificationForm: {
        sourceImportance: undefined,
        audienceTypes: [],
        groupIds: [],
      },

      // 共享数据
      groupList: [],
    };
  },
  methods: {
    // ---- 对外暴露方法 ----
    open(groupList,activeGroup) {
      this.groupList = groupList;
      this.mainDialogVisible = true;
      this.queryParams.direct_id = activeGroup ? activeGroup.id : null;
    },

    // ---- 搜索与列表逻辑 ----
    getList() {
      this.loading = true;
      searchUserFromUniversal(this.queryParams).then(response => {
        this.sourceList = response.data.list || [];
        this.total = response.data.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    resetQuery() {
      const directId = this.queryParams.direct_id;
      this.resetForm("queryForm");
      this.queryParams.direct_id = directId;
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.selectedSources = selection;
    },
    handleMainDialogClose() {
      this.selectedSources = [];
      if (this.$refs.searchTable) {
        this.$refs.searchTable.clearSelection();
      }
    },

    // ---- 添加逻辑 ----
    handleSingleAdd(row) {
      this.selectedSources = [row];
      this.handleOpenClassificationDialog();
    },
    handleOpenClassificationDialog() {
      if (this.selectedSources.length === 0) {
        this.$modal.msgWarning("请至少选择一项要添加的信源");
        return;
      }
      // 重置分类表单
      this.classificationForm = {
        sourceImportance: undefined,
        audienceTypes: [],
        groupIds: [],
      };
      this.classificationDialogVisible = true;
    },
    submitBatchAdd() {
      this.submitLoading = true;
      const details = this.selectedSources.map(item => {
        return {
          userId: item.user_id,
          accountNickname: item.nickname,
          platformUserId: item.short_id || '',
          homepageLink: item.main_domain,
          platform: item.platform,
          secondPlatform: item.platform_name,
          remark: item.remark || "",
          sourceCategory: (['weibo', 'weixin', 'douyin', 'toutiao', 'xiaohongshu'].includes(item.platform)) ? '1' : '2' // 1-账号, 2-站点
        }
      });
      const classificationInfo = {
        groupIds: this.classificationForm.groupIds,
        audienceTypes: this.classificationForm.audienceTypes.join(','),
        sourceImportance: this.classificationForm.sourceImportance
      };

      const payload = {
        details: details,
        classificationInfo: classificationInfo
      };

      batchAddDetail(payload).then(() => {
        this.$modal.msgSuccess(`成功添加 ${details.length} 个信源`);
        this.submitLoading = false;
        this.classificationDialogVisible = false;
        this.mainDialogVisible = false;
        this.$emit("submit-success");
      }).catch(() => {
        this.submitLoading = false;
      })
    }
  }
};
</script>

<style scoped>
.button-link {
  color: #409EFF;
  text-decoration: none;
}

.option-color-dot {
  float: right;
  margin-top: 13px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
</style>