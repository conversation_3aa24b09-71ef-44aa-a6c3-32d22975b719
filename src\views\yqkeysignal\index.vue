<template>
  <div class="app-container key-signal-container">
    <el-row :gutter="20">
      <!--左侧分组管理-->
      <el-col :span="5" :xs="24">
        <GroupPanel ref="groupPanel" @group-selected="handleGroupSelected" @details-changed="handleDetailsChanged"
          @update:group-list="updateMasterGroupList" />
      </el-col>

      <!--右侧信源列表-->
      <el-col :span="19" :xs="24">
        <DetailTable ref="detailTable" :active-group="activeGroup" :group-list="masterGroupList"
          @details-changed="handleDetailsChanged" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import GroupPanel from './components/GroupPanel';
import DetailTable from './components/DetailTable';

export default {
  name: "YqKeySignal",
  components: { GroupPanel, DetailTable },
  data() {
    return {
      masterGroupList: [],// 接收 GroupPanel 传递过来的完整分组列,并将其传递给 DetailTable。
      activeGroup: {
        id: null, // null 表示全部
        name: '全部信源'
      }
    };
  },
  methods: {
    /**
      * 新方法：接收 GroupPanel 传来的完整分组列表，并存储起来
      * 这个列表将传递给 DetailTable，用于其内部的“所属分组”下拉菜单
      * @param {Array} groupList - 来自 GroupPanel 的完整分组列表
      */
    updateMasterGroupList(groupList) {
      this.masterGroupList = groupList;
    },
    /** 处理信源明细变化事件（增、删、改) */
    handleDetailsChanged() {
       // 父组件的这个事件监听器现在只需要知道“有事发生”即可，无需再重复调用子组件的方法。
    },
    /** 处理分组选择事件 */
    handleGroupSelected(group) {
      this.activeGroup = group;
    },
  }
};
</script>

<style lang="scss" scoped>
.key-signal-container {
  // 如果需要，可以添加一些特定样式
}
</style>