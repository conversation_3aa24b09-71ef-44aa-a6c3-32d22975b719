import request from '@/utils/request'

// 查询重点信源明细列表
export function listDetail(query) {
  return request({
    url: '/keySourceDetail/list',
    method: 'post',
    data: query
  })
}

// 批量新增重点信源明细
export function batchAddDetail(data) {
  return request({
    url: '/keySourceDetail/batchAdd',
    method: 'post',
    data: data
  })
}

// 修改重点信源明细
export function updateDetail(data) {
  return request({
    url: '/keySourceDetail/edit',
    method: 'put',
    data: data
  })
}

// 移出重点信源明细
export function delDetail(detailIds) {
  return request({
    url: `/keySourceDetail/remove/${detailIds}`,
    method: 'delete'
  })
}

// 获取重点信源总数和未分组数
export function getDetailCount() {
    return request({
        url: '/keySourceDetail/count',
        method: 'post',
        data: {}
    })
}

// 导出重点信源明细
export function exportDetail(query) {
    return request({
      url: '/keySourceDetail/export',
      method: 'post',
      data: query
    })
}