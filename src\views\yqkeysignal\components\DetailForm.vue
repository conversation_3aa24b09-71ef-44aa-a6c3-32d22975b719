<template>
  <el-dialog title="修改信源信息" :visible.sync="visible" width="600px" append-to-body>
    <el-form ref="form" :model="form" label-width="100px" v-if="form.id">
      <el-form-item label="账号昵称">
        <span>{{ form.accountNickname }}</span>
      </el-form-item>

      <el-form-item label="级别分类" prop="sourceImportance">
        <el-select v-model="form.sourceImportance" placeholder="请选择级别分类" style="width: 100%;">
          <el-option v-for="dict in dict.type.source_importance" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="网民类型" prop="audienceTypes">
        <el-select v-model="form.audienceTypes" multiple placeholder="请选择网民类型 (可多选)" style="width: 100%;">
          <el-option v-for="dict in dict.type.audience_type" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="所属分组" prop="groupIds">
        <el-select v-model="form.groupIds" multiple placeholder="请选择所属分组 (可多选)" style="width: 100%;">
          <el-option v-for="group in groupList" :key="group.id" :label="group.groupName" :value="group.id">
            <span style="float: left">{{ group.groupName }}</span>
            <span class="option-color-dot" :style="{ backgroundColor: group.groupColor }"></span>
          </el-option>
        </el-select>
      </el-form-item>

    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitForm">保存修改</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateDetail } from "@/api/yqkeysignal/detail";

export default {
  name: "DetailForm",
  dicts: ['audience_type', 'source_importance'],
  data() {
    return {
      visible: false,
      form: {},
      groupList: [],
    };
  },
  methods: {
    open(data, groupList) {
      this.reset();
      this.groupList = groupList;

      // [核心修复] 深拷贝数据，并手动将 detailId 映射到 form.id
      // 这样，后续的 this.form.id 判断和使用都能正常工作
      const formData = JSON.parse(JSON.stringify(data));
      formData.id = data.detailId; // <-- 关键修复！

      // --- 以下为处理下拉框数据的逻辑，保持不变 ---
      if (formData.audienceTypes && typeof formData.audienceTypes === 'string') {
        formData.audienceTypes = formData.audienceTypes.split(',');
      } else if (!formData.audienceTypes) {
        formData.audienceTypes = [];
      }
      // 后端返回的明细对象中没有 groups 数组，所以这个逻辑可能不需要
      // 但保留它以防万一
      formData.groupIds = data.groups ? data.groups.map(g => g.groupId) : (data.groupIds || []);

      this.form = formData;
      this.visible = true;
    },
    reset() {
      this.form = {
        id: undefined,
        accountNickname: undefined,
        sourceImportance: undefined,
        audienceTypes: [],
        groupIds: []
      };
      this.resetForm("form");
    },
    cancel() {
      this.visible = false;
      this.reset();
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const submitData = {
            detailId: this.form.id,
            sourceImportance: this.form.sourceImportance,
            audienceTypes: this.form.audienceTypes.join(','),
            groupIds: this.form.groupIds
          };

          updateDetail(submitData).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.visible = false;
            this.$emit("submit-success");
          });
        }
      });
    }
  }
};
</script>
<style scoped>
.option-color-dot {
  float: right;
  margin-top: 13px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}
</style>