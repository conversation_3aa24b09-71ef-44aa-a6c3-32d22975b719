import request from '@/utils/request'

// 查询重点信源分组列表
export function listGroup(query) {
  return request({
    url: '/keySourceGroup/list',
    method: 'post',
    data: query
  })
}

// 新增重点信源分组
export function addGroup(data) {
  return request({
    url: '/keySourceGroup/add',
    method: 'post',
    data: data
  })
}

// 修改重点信源分组
export function updateGroup(groupId, data) {
  return request({
    url: `/keySourceGroup/edit/${groupId}`,
    method: 'put',
    data: data
  })
}

// 删除重点信源分组
export function delGroup(groupIds) {
  return request({
    url: `/keySourceGroup/remove/${groupIds}`,
    method: 'delete'
  })
}

// 设置重点信源分组
export function updateGroupSettings(groupId, data) {
    return request({
        url: `/keySourceGroup/settings/${groupId}`,
        method: 'put',
        data: data
    })
}