<template>
    <div>
        <el-card shadow="never">
            <div slot="header" class="clearfix">
                <span>{{ activeGroup.name || '全部信源' }}</span>
            </div>

            <!-- 搜索和操作区域 -->
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
                <el-form-item label="账号昵称" prop="accountNickname">
                    <el-input v-model="queryParams.accountNickname" placeholder="请输入账号昵称" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="用户ID" prop="userId">
                    <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="平台用户ID" prop="platformUserId">
                    <el-input v-model="queryParams.platformUserId" placeholder="请输入平台用户ID" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>

                <el-form-item label="主页链接" prop="homepageLink">
                    <el-input v-model="queryParams.homepageLink" placeholder="请输入主页链接" clearable
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <!-- 修改“级别分类”的 el-select，添加 multiple -->
                <el-form-item label="级别分类" prop="sourceImportance">
                    <el-select v-model="queryParams.sourceImportance" multiple placeholder="全部级别" clearable
                        style="width: 150px">
                        <el-option v-for="dict in dict.type.source_importance" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                    </el-select>
                </el-form-item>

                <!-- 修改“网民类型”的 el-select，添加 multiple -->
                <el-form-item label="网民类型" prop="audienceTypes">
                    <el-select v-model="queryParams.audienceTypes" multiple placeholder="全部类型" clearable
                        style="width: 150px">
                        <el-option v-for="dict in dict.type.audience_type" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                    </el-select>
                </el-form-item>

                <!-- 修改“来源类别”的 el-select，添加 multiple -->
                <el-form-item label="来源类别" prop="sourceCategories">
                    <el-select v-model="queryParams.sourceCategories" multiple placeholder="全部类别" clearable
                        style="width: 150px">
                        <el-option label="账号" value="1"></el-option>
                        <el-option label="站点" value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="添加时间">
                    <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini"
                        @click="handleAdd">从信源库添加</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                        v-hasPermi="['yq:keysignal:export']">导出数据</el-button>
                </el-col>
                <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <!-- 数据表格 -->
            <el-table v-loading="loading" :data="detailList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column label="账号昵称" align="left" prop="accountNickname" width="180">
                    <template slot-scope="scope">
                        <div>{{ scope.row.accountNickname }}</div>
                        <div class="sub-text">{{ scope.row.remark }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="用户ID" align="left" prop="userId" width="150" :show-overflow-tooltip="true" />
                <el-table-column label="平台" align="center" prop="platform" width="100">
                    <template slot-scope="scope">
                        <el-tag size="small">{{ scope.row.platformName || scope.row.platform }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="二级平台" align="center" prop="secondPlatform" width="100">
                    <template slot-scope="scope">
                        <el-tag type="info" size="small" v-if="scope.row.secondPlatform">{{ scope.row.secondPlatform }}</el-tag>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="主页链接" align="center" width="100">
                    <template slot-scope="scope">
                        <a :href="scope.row.homepageLink" target="_blank" class="button-link">查看链接</a>
                    </template>
                </el-table-column>
                <el-table-column label="所属分组" align="left" prop="groups" width="200">
                    <template slot-scope="scope">
                        <el-tag v-for="group in scope.row.groups" :key="group.id"
                            :style="{ 'background-color': group.groupColor + '20', 'border-color': group.groupColor + '80', color: group.groupColor }"
                            size="small" style="margin-right: 5px; margin-bottom: 5px">
                            {{ group.groupName }}
                        </el-tag>
                        <span v-if="!scope.row.groups || scope.row.groups.length === 0">--</span>
                    </template>
                </el-table-column>
                <el-table-column label="网民类型" align="left" prop="audienceTypes" width="180">
                    <template slot-scope="scope">
                        <el-tag v-for="type in scope.row.audienceTypeNames" :key="type" type="info" size="small"
                            style="margin-right: 5px; margin-bottom: 5px">
                            {{ type }}
                        </el-tag>
                        <span v-if="!scope.row.audienceTypeNames || scope.row.audienceTypeNames.length === 0">--</span>
                    </template>
                </el-table-column>
                <el-table-column label="信源类别" align="center" prop="sourceCategory" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.sourceCategory === '1' ? 'success' : 'warning'" size="small" v-if="scope.row.sourceCategoryName">
                            {{ scope.row.sourceCategoryName }}
                        </el-tag>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="级别分类" align="center" prop="sourceImportance" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="getImportanceTagType(scope.row.sourceImportance)" size="small"
                            v-if="scope.row.sourceImportanceName">{{ scope.row.sourceImportanceName }}</el-tag>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="添加时间" align="center" prop="createTime" width="160" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right"
                    width="120">
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" icon="el-icon-edit"
                            @click="handleUpdate(scope.row)">编辑</el-button>
                        <el-button size="mini" type="text" icon="el-icon-delete"
                            @click="handleDelete(scope.row)">移出</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>

        <!-- 添加或修改信源分类对话框 -->
        <DetailForm ref="detailForm" @submit-success="handleRefresh" />
        <!-- 从信源库添加对话框 -->
        <AddFromLibraryDialog ref="addFromLibrary" @submit-success="handleRefresh" />
    </div>
</template>

<script>
import { listDetail, delDetail, exportDetail } from "@/api/yqkeysignal/detail";
import DetailForm from './DetailForm';
import AddFromLibraryDialog from './AddFromLibraryDialog';
import { getDicts } from "@/api/system/dict/data";

export default {
    name: "DetailTable",
    dicts: ['audience_type', 'source_importance', 'content_platform'],
    components: { DetailForm, AddFromLibraryDialog },
    props: {
        activeGroup: {
            type: Object,
            required: true
        },
        // 3. 添加 groupList prop
        groupList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            loading: false,
            showSearch: true,
            ids: [],
            single: true,
            multiple: true,
            total: 0,
            detailList: [],
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                accountNickname: undefined,
                userId: undefined,
                platformUserId: undefined,
                homepageLink: undefined,
                groupIds: [],
                ungrouped: false,
                sourceImportance: [],
                audienceTypes: [],
                sourceCategories: []
            },
            dateRange: [],
            isMounted: false
        }
    },
    mounted() {
        this.isMounted = true;
    },
    watch: {
        activeGroup: {
            handler(newGroup) {
                this.queryParams.pageNum = 1;
                this.queryParams.accountNickname = undefined;
                this.queryParams.userId = undefined;
                this.queryParams.platformUserId = undefined;
                this.queryParams.homepageLink = undefined;
                this.queryParams.sourceCategories = undefined;
                this.dateRange = [];

                if (this.isMounted) {
                    this.$refs.queryForm.resetFields();
                }

                if (newGroup.id === 0) {
                    this.queryParams.ungrouped = true;
                    this.queryParams.groupIds = [];
                } else if (newGroup.id === null) {
                    this.queryParams.ungrouped = false;
                    this.queryParams.groupIds = [];
                } else {
                    this.queryParams.ungrouped = false;
                    this.queryParams.groupIds = [newGroup.id];
                }

                this.getList();
            },
            immediate: true
        },
        // 当分组列表变化时，重新为当前明细数据构建友好的分组展示结构
        groupList: {
            handler() {
                if (!this.detailList || this.detailList.length === 0) return;
                this.detailList = this.detailList.map(item => {
                    item.groups = this.buildGroupsForItem(item);
                    return item;
                });
            }
        }
    },
    methods: {
        // 将后端的 groupIds/groupIdsStr 映射为用于展示的 groups 数组
        buildGroupsForItem(item) {
            const defaultColor = '#409EFF';
            // 1) 解析分组ID列表
            let ids = [];
            if (Array.isArray(item.groupIds)) {
                ids = item.groupIds;
            } else if (typeof item.groupIdsStr === 'string' && item.groupIdsStr.trim() !== '') {
                ids = item.groupIdsStr.split(',').map(v => Number(v)).filter(v => !Number.isNaN(v));
            }

            // 2) 解析同位置的名称（可选，用作兜底）
            let namesByIndex = [];
            if (Array.isArray(item.groupNames)) {
                namesByIndex = item.groupNames;
            } else if (typeof item.groupNamesStr === 'string') {
                namesByIndex = item.groupNamesStr.split(',');
            }

            // 3) 基于 master groupList 做映射，拿到颜色与名称
            const mapped = ids.map((id, index) => {
                const found = this.groupList.find(g => g.groupId === id);
                if (found) {
                    return {
                        id: found.groupId,
                        groupName: found.groupName,
                        groupColor: found.groupColor || defaultColor
                    };
                }
                // 兜底：仅有名称时，也展示标签（使用默认颜色）
                const fallbackName = namesByIndex[index] || String(id);
                return {
                    id,
                    groupName: fallbackName,
                    groupColor: defaultColor
                };
            });

            return mapped;
        },
        mapData(data) {
            return data.map(item => {
                const audienceTypeIds = item.audienceTypes ? String(item.audienceTypes).split(',') : [];
                item.audienceTypeNames = audienceTypeIds.map(id => {
                    const dict = this.dict.type.audience_type.find(d => d.value === id);
                    return dict ? dict.label : '';
                }).filter(Boolean);

                const importanceDict = this.dict.type.source_importance.find(d => d.value === item.sourceImportance);
                item.sourceImportanceName = importanceDict ? importanceDict.label : '';

                // 平台名称：若后端 platform 是编码，尝试用 content_platform 字典映射
                const platformDict = this.dict.type.content_platform && this.dict.type.content_platform.find(d => d.value === item.platform);
                item.platformName = platformDict ? platformDict.label : (item.secondPlatform || item.platform);

                // 信源类别中文名
                if (item.sourceCategory === '1') {
                    item.sourceCategoryName = '账号';
                } else if (item.sourceCategory === '2') {
                    item.sourceCategoryName = '站点';
                } else {
                    item.sourceCategoryName = '';
                }

                // 根据当前分组列表，构建友好的分组展示结构
                item.groups = this.buildGroupsForItem(item);

                return item;
            })
        },
        getList() {
            this.loading = true;
            const params = { ...this.queryParams };
            if (this.dateRange && this.dateRange.length) {
                params.addTimeStart = this.dateRange[0] + " 00:00:00";
                params.addTimeEnd = this.dateRange[1] + " 23:59:59";
            } else {
                params.addTimeStart = undefined;
                params.addTimeEnd = undefined;
            }
            listDetail(params).then(response => {
                this.detailList = this.mapData(response.rows);
                this.total = response.total;
                this.loading = false;
                
                // 打印 detailList 用于调试
                console.log('DetailTable detailList:', this.detailList);
            })
        },
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        resetQuery() {
            this.dateRange = [];
            this.resetForm("queryForm");
            this.handleQuery();
        },
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.detailId);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        handleAdd() {
            this.$refs.addFromLibrary.open(this.groupList, this.activeGroup);
        },
        handleUpdate(row) {
            this.$refs.detailForm.open(row, this.groupList);
        },
        handleDelete(row) {
            const detailIds = row.detailId || this.ids;
            this.$modal.confirm(`是否确认移出账号昵称为【${row.accountNickname}】的信源？`).then(() => {
                return delDetail(detailIds);
            }).then(() => {
                this.handleRefresh();
                this.$modal.msgSuccess("移出成功");
            }).catch(() => { });
        },
        handleExport() {
            // [关键] 预先定义好 application/json 的请求配置
            const jsonPostOptions = {
                headers: { 'Content-Type': 'application/json' },
                // transformRequest 会在请求发送前处理数据，此处是将JS对象序列化为JSON字符串
                transformRequest: [(data) => JSON.stringify(data)]
            };

            // 判断是否有选中的行
            if (this.ids && this.ids.length > 0) {
                // --- 情况一：用户勾选了行，只导出选中项 ---

                // 准备只包含选中ID的参数
                const exportParams = { ids: this.ids };

                this.$modal.confirm(`是否确认导出您选中的 ${this.ids.length} 条数据？`).then(() => {
                    // [修复] 调用 download 时，传入 jsonPostOptions
                    this.download('keySourceDetail/export', exportParams, `重点信源(选中)_${new Date().getTime()}.xlsx`, jsonPostOptions);
                }).catch(() => { });

            } else {
                // --- 情况二：用户未勾选任何行，按原逻辑导出全部筛选结果 ---

                // 准备包含所有搜索条件的参数
                const exportParams = { ...this.queryParams };
                if (this.dateRange && this.dateRange.length) {
                    exportParams.addTimeStart = this.dateRange[0] + " 00:00:00";
                    exportParams.addTimeEnd = this.dateRange[1] + " 23:59:59";
                }

                this.$modal.confirm('您未选择任何数据，将导出所有符合当前筛选条件的数据。是否继续？').then(() => {
                    // [修复] 调用 download 时，也传入 jsonPostOptions
                    this.download('keySourceDetail/export', exportParams, `重点信源(全部)_${new Date().getTime()}.xlsx`, jsonPostOptions);
                }).catch(() => { });
            }
        },
        handleRefresh() {
            this.getList();
            this.$emit('details-changed');
        },
        getImportanceTagType(level) {
            if (level === '1') return 'danger';
            if (level === '2') return 'warning';
            return 'info';
        }
    }
}
</script>
<style scoped>
.sub-text {
    font-size: 12px;
    color: #999;
}

.button-link {
    color: #409EFF;
    text-decoration: none;
}
</style>