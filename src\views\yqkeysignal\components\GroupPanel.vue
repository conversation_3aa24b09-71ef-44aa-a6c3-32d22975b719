<template>
  <div class="group-panel-wrapper">
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>分组管理 ({{ total }}个分组)</span>
        <div class="header-buttons">
          <el-button icon="el-icon-plus" circle size="mini" @click="handleAdd"></el-button>
        </div>
      </div>
      <div class="group-search-form">
        <el-form :model="groupQueryParams" ref="groupQueryForm" size="mini" :inline="true" class="group-query-form">
          <el-form-item prop="keyword">
            <el-input v-model="groupQueryParams.keyword" placeholder="分组名称/描述" clearable style="width: 100%;"
              @keyup.enter.native="handleGroupQuery" />
          </el-form-item>
          <el-form-item prop="syncCreateFlag">
            <el-select v-model="groupQueryParams.syncCreateFlag" placeholder="是否定向" clearable style="width: 100%;">
              <el-option v-for="dict in dict.type.sync_create_flag" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="monitorFeatureFlag">
            <el-select v-model="groupQueryParams.monitorFeatureFlag" placeholder="是否应用特征" clearable
              style="width: 100%;">
              <el-option v-for="dict in dict.type.monitor_feature_flag" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleGroupQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetGroupQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="group-list">
        <!-- 全部信源 -->
        <div class="group-item" :class="{ 'active': activeGroupId === null }" @click="handleSelectGroup(null, '全部信源')">
          <div class="group-info">
            <span class="group-name">全部信源</span>
          </div>
          <span class="group-count">{{ counts.total || 0 }}</span>
        </div>

        <!-- 未分组 -->
        <div class="group-item" :class="{ 'active': activeGroupId === 0 }" @click="handleSelectGroup(0, '未分组')">
          <div class="group-info">
            <span class="group-name">未分组</span>
          </div>
          <span class="group-count">{{ counts.ungrouped || 0 }}</span>
        </div>

        <!-- 新增：添加分割线和标题以区分区域 -->
        <el-divider v-if="groupList.length > 0" content-position="left" class="list-divider">已分组</el-divider>

        <!-- 分组列表 -->
        <el-scrollbar class="group-list-scrollbar" v-loading="loading">
          <!-- 确保 v-for 和 :key, :class 的正确绑定 -->
          <div v-for="group in groupList" :key="group.groupId" class="group-item"
            :class="{ 'active': activeGroupId === group.groupId }"
            @click="handleSelectGroup(group.groupId, group.groupName)">
            <div class="group-info">
              <span class="group-color-dot" :style="{ backgroundColor: group.groupColor }" v-if="group.groupColor"></span>
              <div class="name-desc">
                <el-tooltip class="item" effect="dark" :content="group.groupName" placement="top">
                  <div class="group-name">{{ group.groupName }}</div>
                </el-tooltip>
                <el-tooltip v-if="group.groupDescription" class="item" effect="dark" :content="group.groupDescription"
                  placement="top">
                  <div class="group-description">{{ group.groupDescription }}</div>
                </el-tooltip>
              </div>
            </div>
            <div class="group-actions">
              <el-button type="text" icon="el-icon-edit" size="mini" @click.stop="handleEdit(group)"></el-button>
              <el-button type="text" icon="el-icon-setting" size="mini" @click.stop="handleSettings(group)"></el-button>
              <el-button type="text" icon="el-icon-delete" size="mini" @click.stop="handleDelete(group)"></el-button>
            </div>
          </div>
        </el-scrollbar>

      </div>
    </el-card>

    <!-- 分组表单弹窗 -->
    <GroupForm ref="groupForm" @submit-success="handleGroupsChange" />
    <!-- 分组设置弹窗 -->
    <GroupSettings ref="groupSettings" @submit-success="handleGroupsChange" />

  </div>
</template>

<script>
import { listGroup, delGroup } from "@/api/yqkeysignal/group";
import { getDetailCount } from "@/api/yqkeysignal/detail";
import GroupForm from './GroupForm';
import GroupSettings from './GroupSettings';

export default {
  name: "GroupPanel",
  dicts: ['sync_create_flag', 'monitor_feature_flag'],
  components: { GroupForm, GroupSettings },
  props: {},
  data() {
    return {
      loading: false,
      groupList: [],
      total: 0,
      activeGroupId: null, // null for '全部信源', 0 for '未分组'
      counts: {
        total: 0,
        ungrouped: 0
      },
      groupQueryParams: {
        pageNum: 1, // 分页页码
        pageSize: 1000, // 每页数量（设置一个较大值）
        keyword: undefined,
        syncCreateFlag: undefined,
        monitorFeatureFlag: undefined
      }
    };
  },
  created() {
    this.getList(); // 首次加载分组列表
    this.refreshCounts();
  },
  methods: {
    /** 查询分组列表 */
    getList() {
      this.loading = true; // 开始加载
      // 直接调用 API，并传入当前搜索条件
      return listGroup(this.groupQueryParams).then(response => {
        // 对 response.rows 进行 map，验证 groupColor 是否为有效的颜色值
        this.groupList = response.rows.map(item => {
          // 检查 groupColor 是否为有效的颜色值（形如 '#FFFFFF'）
          if (item.groupColor && !/^#[0-9A-Fa-f]{6}$/.test(item.groupColor)) {
            item.groupColor = '#67C23A'; // 设置为默认颜色
          }
          return item;
        });
        this.total = response.total; // 从 API 响应中获取真实的总数
        this.loading = false; // 加载完成
        this.$emit('update:group-list', this.groupList);
      }).catch(() => {
        this.loading = false; // 加载失败
      });
    },
    /** 刷新信源计数 */
    refreshCounts() {
      getDetailCount().then(res => {
        this.counts.total = res.data.totalCount;
        this.counts.ungrouped = res.data.ungroupedCount;
      })
    },
    handleGroupQuery() {
      this.getList().then(() => {
        // 搜索完成后，检查当前 selection 是否仍然有效
        const currentSelectionExists = this.groupList.some(g => g.groupId === this.activeGroupId);

        // 如果 activeGroupId 不是“全部信源”(null)或“未分组”(0)，
        // 并且它在新的 groupList 中已经不存在了
        if (this.activeGroupId !== null && this.activeGroupId !== 0 && !currentSelectionExists) {
          // 那么就将选择重置为“全部信源”，以避免一个无效的“悬空”选择
          this.$modal.msgWarning("原选中分组已被筛除，已自动切换至“全部信源”");
          this.handleSelectGroup(null, '全部信源');
        }
        // 如果之前的选择仍然有效（比如之前选的就是“全部信源”，或者选中的分组仍在筛选结果中），
        // 则什么也不做，保持原样。这样就防止了自动选择第一个结果的BUG。
      });
    },
    resetGroupQuery() {
      this.resetForm("groupQueryForm");
      this.handleGroupQuery();
    },
    /** 选择分组 */
    handleSelectGroup(id, name) {
      if (this.activeGroupId === id) return;
      this.activeGroupId = id;
      this.$emit('group-selected', { id, name });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.groupForm.open('add');
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.$refs.groupForm.open('edit', row);
    },
    /** 设置按钮操作 */
    handleSettings(row) {
      this.$refs.groupSettings.open(row);
    },
    handleGroupsChange() {
      this.getList(); // 1. 刷新自己的分组列表
      this.refreshCounts(); // 2. 刷新“全部”和“未分组”计数
      this.$emit('details-changed'); // 3. 通知父组件（名称保持一致，让父组件刷新信源明细和全局分组列表）
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const groupIds = row.groupId;
      const groupName = row.groupName;
      this.$modal.confirm(`确定要删除分组【${groupName}】吗？删除后，该分组下的信源将变为未分组状态。`).then(() => {
        return delGroup(groupIds);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.handleGroupsChange(); // 调用统一处理函数
        // 如果删除的是当前选中的分组，则切换到“全部信源”
        if (this.activeGroupId === groupIds) {
          this.handleSelectGroup(null, '全部信源');
        }
      }).catch(() => { });
    },
  }
};
</script>

<style lang="scss" scoped>
.group-panel-wrapper .el-card__header {
  padding: 12px 15px;
}

.clearfix {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-list-scrollbar {
  height: calc(100vh - 280px);
  /* 根据实际情况调整 */
  overflow-x: hidden;
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 5px;
  font-size: 14px;

  .group-info {
    display: flex;
    align-items: center;
    flex-grow: 1;
    overflow: hidden;
  }

  .group-color-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .name-desc {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .group-name {
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .group-description {
    font-size: 12px;
    color: #999;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .group-count {
    color: #909399;
    margin-left: 10px;
  }

  .group-actions {
    display: none;
  }

  &:hover {
    background-color: #f5f7fa;

    .group-actions {
      display: block;
    }
  }

  &.active {
    background-color: #ecf5ff;
    color: #409eff;
  }
}

.group-search-form {
  padding: 0 10px 10px 10px;
  border-bottom: 1px solid #e6ebf5;

  .el-form-item {
    margin-bottom: 0;
  }
}

.group-query-form {
  .el-form-item {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.list-divider {
  margin: 15px 0 10px 0 !important; // 增加上下边距，确保与内容分开

  .el-divider__text {
    font-size: 13px;
    color: #a9a9a9;
    font-weight: normal;
  }
}
</style>