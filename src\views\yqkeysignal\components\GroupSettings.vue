<template>
    <el-dialog title="分组设置" :visible.sync="visible" width="600px" append-to-body>
        <div v-if="form.id">
            <div class="setting-item">
                <div class="setting-title">{{ form.groupName }}</div>
                <div class="setting-desc">{{ form.groupDescription }}</div>
            </div>
            <el-divider></el-divider>

            <el-form ref="form" :model="settings" label-position="top">
                <el-form-item>
                    <el-checkbox v-model="settings.syncCreateFlag" true-label="1"
                        false-label="0">同步设置为定向信源组</el-checkbox>
                    <div class="setting-help-text">启用后，该分组将同步为定向信源组，可在专题中使用。</div>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="settings.monitorFeatureFlag" true-label="1"
                        false-label="0">应用于监测舆情特征</el-checkbox>
                    <div class="setting-help-text">应用后，该分组内的信源将用于舆情特征分析，帮助识别和分析相关舆情趋势。</div>
                </el-form-item>
            </el-form>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitForm">保存设置</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { updateGroupSettings } from "@/api/yqkeysignal/group";

export default {
    name: "GroupSettings",
    data() {
        return {
            visible: false,
            form: {},
            settings: {
                syncCreateFlag: "0",
                monitorFeatureFlag: "0"
            }
        }
    },
    methods: {
        open(data) {
            this.form = {
                ...data,
                id: data.groupId
            };
            this.settings.syncCreateFlag = data.syncCreateFlag || '0';
            this.settings.monitorFeatureFlag = data.monitorFeatureFlag || '0';
            this.visible = true;
        },
        cancel() {
            this.visible = false;
        },
        submitForm() {
            updateGroupSettings(this.form.id, this.settings).then(() => {
                this.$modal.msgSuccess("设置成功");
                this.visible = false;
                this.$emit("submit-success");
            });
        }
    }
}
</script>

<style scoped>
.setting-item {
    margin-bottom: 20px;
}

.setting-title {
    font-size: 16px;
    font-weight: bold;
}

.setting-desc {
    font-size: 14px;
    color: #999;
    margin-top: 5px;
}

.setting-help-text {
    font-size: 12px;
    color: #999;
    line-height: 1.5;
    margin-left: 25px;
    margin-top: -5px;
}
</style>