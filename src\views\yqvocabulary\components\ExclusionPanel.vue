<template>
  <el-row :gutter="20">
    <!-- 左侧：分组管理 -->
    <el-col :span="6" :xs="24">
      <div class="head-container">
        <el-input
          v-model="groupName"
          placeholder="请输入分组名称"
          clearable
          size="small"
          prefix-icon="el-icon-search"
          style="margin-bottom: 20px"
        />
      </div>
      <div class="head-container" style="margin-bottom: 10px;">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAddGroup"
          v-hasPermi="['system:dictionaryGroup:add']"
        >新增分组</el-button>
         <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="singleGroup"
          @click="handleDeleteGroup"
          v-hasPermi="['system:dictionaryGroup:remove']"
        >删除分组</el-button>
      </div>
      <el-table
        v-loading="groupLoading"
        :data="groupList.filter(data => !groupName || data.groupName.toLowerCase().includes(groupName.toLowerCase()))"
        @current-change="handleGroupChange"
        highlight-current-row
        ref="groupTable"
      >
        <el-table-column label="分组名称" align="center" prop="groupName" :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" width="100">
           <template slot-scope="scope">
             <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click.stop="handleUpdateGroup(scope.row)"
                v-hasPermi="['system:dictionaryGroup:edit']"
              ></el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click.stop="handleDeleteGroup(scope.row)"
                v-hasPermi="['system:dictionaryGroup:remove']"
              ></el-button>
           </template>
        </el-table-column>
      </el-table>
       <pagination
        v-show="groupTotal>0"
        :total="groupTotal"
        :page.sync="groupQueryParams.pageNum"
        :limit.sync="groupQueryParams.pageSize"
        layout="total, prev, pager, next"
        @pagination="getGroupList"
      />
    </el-col>

    <!-- 右侧：词汇管理 -->
    <el-col :span="18" :xs="24">
      <!-- 操作栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAddWord" v-hasPermi="['system:dictionaryWord:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multipleWord" @click="handleDeleteWord" v-hasPermi="['system:dictionaryWord:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="multipleWord" @click="handleBatchSetGroup" v-hasPermi="['system:dictionaryWord:edit']">批量设置分组</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport" v-hasPermi="['system:dictionaryWord:import']">导入</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:dictionaryWord:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getWordList"></right-toolbar>
      </el-row>
      
      <!-- 搜索栏 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="关键词" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter.native="handleQuery"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table v-loading="wordLoading" :data="wordList" @selection-change="handleWordSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" :index="wordIndex" width="60" align="center" />
        <el-table-column label="词汇文本" align="center" prop="wordText" />
        <el-table-column label="所属分组" align="center" prop="groups">
          <template slot-scope="scope">
            <el-tag
              v-for="group in scope.row.groups"
              :key="group.groupId"
              size="small"
              style="margin-right: 5px;"
            >{{ group.groupName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdateWord(scope.row)" v-hasPermi="['system:dictionaryWord:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteWord(scope.row)" v-hasPermi="['system:dictionaryWord:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="wordTotal>0" :total="wordTotal" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getWordList" />
    </el-col>

    <!-- 分组表单弹窗 -->
    <group-form ref="groupForm" :category-info="categoryInfo" @submit-success="getGroupList" />
    <!-- 词汇表单弹窗 -->
    <word-form ref="wordForm" :category-info="categoryInfo" @submit-success="getWordList" />
    
    <!-- 批量设置分组弹窗 -->
    <el-dialog title="批量设置分组" :visible.sync="batchSetGroupVisible" width="500px" append-to-body>
        <el-select v-model="batchGroupIds" multiple placeholder="请选择要设置的分组" style="width: 100%;">
            <el-option
                v-for="group in allGroupList"
                :key="group.groupId"
                :label="group.groupName"
                :value="group.groupId">
            </el-option>
        </el-select>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitBatchSetGroup">确 定</el-button>
            <el-button @click="batchSetGroupVisible = false">取 消</el-button>
        </div>
    </el-dialog>
    
    <!-- [已修正] 导入弹窗 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的词汇数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </el-row>
</template>

<script>
import {
  listDictionaryGroups, deleteDictionaryGroups,
  searchDictionaryWords, deleteDictionaryWords, batchSetWordGroups,
  getDictionaryGroupsByCategory, downloadImportTemplate
} from '@/api/yqvocabulary';
import GroupForm from './GroupForm.vue';
import WordForm from './WordForm.vue';
import { getToken } from "@/utils/auth";

export default {
  name: "ExclusionPanel",
  components: { GroupForm, WordForm },
  props: {
    categoryInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 左侧分组管理
      groupLoading: true,
      groupList: [],
      groupTotal: 0,
      groupName: '', // 分组搜索关键词
      groupQueryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryId: null,
      },
      currentGroup: null, // 当前选中的分组
      singleGroup: true, // 单个分组禁用

      // 右侧词汇管理
      wordLoading: true,
      wordList: [],
      wordTotal: 0,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: undefined,
        categoryId: null,
        groupIdList: undefined // 按分组查询
      },
      selectedWordIds: [], // 选中的词汇ID
      multipleWord: true, // 多个词汇禁用
      
      // 批量设置分组
      batchSetGroupVisible: false,
      batchGroupIds: [],
      allGroupList: [], // 用于批量设置弹窗的全部分组列表

      // [新增] 导入相关数据
      upload: {
        open: false,
        title: "",
        isUploading: false,
        updateSupport: 0,
        headers: { Authorization: "Bearer " + getToken() },
        url: process.env.VUE_APP_BASE_API + "/system/dictionaryWord/importData"
      },
    };
  },
  watch: {
    categoryInfo: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.categoryId) {
          this.groupQueryParams.categoryId = newVal.categoryId;
          this.queryParams.categoryId = newVal.categoryId;
          this.getGroupList();
          this.getWordList();
        }
      }
    }
  },
  methods: {
    // 表格序号（跨页自增）
    wordIndex(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
    },
    // --- 分组管理 ---
    getGroupList() {
      this.groupLoading = true;
      listDictionaryGroups(this.groupQueryParams).then(response => {
        this.groupList = response.rows;
        this.groupTotal = response.total;
        this.groupLoading = false;
        // 默认选中第一个
        if (this.groupList.length > 0 && !this.currentGroup) {
          this.$refs.groupTable.setCurrentRow(this.groupList[0]);
        }
      });
    },
    handleAddGroup() {
      this.$refs.groupForm.open();
    },
    handleUpdateGroup(row) {
      this.$refs.groupForm.open(row);
    },
    handleDeleteGroup(row) {
      const groupIds = row.groupId || this.currentGroup.groupId;
      this.$modal.confirm(`是否确认删除分组ID为"${groupIds}"的数据项？`).then(() => {
        return deleteDictionaryGroups(groupIds);
      }).then(() => {
        this.getGroupList();
        this.currentGroup = null; // 删除后清空选择
        this.getWordList(); // 刷新词汇列表
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleGroupChange(val) {
      this.currentGroup = val;
      this.singleGroup = !val;
      if (val) {
        this.queryParams.groupIdList = String(val.groupId);
      } else {
        this.queryParams.groupIdList = undefined;
      }
      this.handleQuery();
    },

    // --- 词汇管理 ---
    getWordList() {
      this.wordLoading = true;
      searchDictionaryWords(this.queryParams).then(response => {
        this.wordList = response.rows;
        this.wordTotal = response.total;
        this.wordLoading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getWordList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAddWord() {
      this.$refs.wordForm.open();
    },
    handleUpdateWord(row) {
      this.$refs.wordForm.open(row);
    },
    handleDeleteWord(row) {
      const wordIds = row.wordId || this.selectedWordIds;
      this.$modal.confirm(`是否确认删除ID为"${wordIds}"的词汇项？`).then(() => {
        return deleteDictionaryWords(wordIds);
      }).then(() => {
        this.getWordList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleWordSelectionChange(selection) {
      this.selectedWordIds = selection.map(item => item.wordId);
      this.multipleWord = !selection.length;
    },

    // --- 批量操作 ---
    handleBatchSetGroup() {
      this.batchGroupIds = [];
      getDictionaryGroupsByCategory(this.categoryInfo.categoryId).then(response => {
        this.allGroupList = response.data;
        this.batchSetGroupVisible = true;
      });
    },
    submitBatchSetGroup() {
      const data = {
        wordIds: this.selectedWordIds,
        groupIds: this.batchGroupIds,
        operation: 'set'
      };
      batchSetWordGroups(data).then(() => {
        this.$modal.msgSuccess("批量设置成功");
        this.batchSetGroupVisible = false;
        this.getWordList();
      });
    },

    // --- [新增] 导入导出方法 ---
    handleImport() {
      this.upload.title = "词汇导入";
      this.upload.open = true;
    },
    handleExport() {
        const exportParams = {
            categoryIds: [this.categoryInfo.categoryId],
            groupIds: this.currentGroup ? [this.currentGroup.groupId] : []
        };
        const confirmMsg = this.currentGroup
            ? `是否确认导出分组[${this.currentGroup.groupName}]下的所有词汇数据项？`
            : `是否确认导出所有排除词数据项？`;

        this.$modal.confirm(confirmMsg).then(() => {
            this.download('/system/dictionaryWord/export', exportParams, `exclusion_words_${new Date().getTime()}.xlsx`, {
                headers: { 'Content-Type': 'application/json' },
                transformRequest: [function (data) {
                    return JSON.stringify(data)
                }]
            });
        }).catch(() => {});
    },
    importTemplate() {
      downloadImportTemplate().then(response => {
        this.$download.name(response.msg);
      });
    },
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getWordList();
    },
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>

<style scoped>
.head-container {
  margin-bottom: 10px;
}
</style>